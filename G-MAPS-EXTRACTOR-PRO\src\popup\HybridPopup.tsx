import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  TextField,
  FormControlLabel,
  Switch,
  Slider,
  Alert,
  CircularProgress,
  Chip,
  Grid,
  Divider,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Badge
} from '@mui/material';
import {
  Search as SearchIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  NetworkCheck as NetworkIcon,
  Storage as StorageIcon,
  Code as CodeIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';

interface BusinessData {
  id: string;
  name: string;
  address: string;
  category: string;
  rating?: number;
  reviewCount?: number;
  phone?: string;
  website?: string;
  extractedAt: Date;
  source: string;
  confidence?: number;
  sources?: string[];
}

interface ExtractionStats {
  totalFound: number;
  networkResults: number;
  internalResults: number;
  domResults: number;
  confidence: number;
  executionTime: number;
}

const HybridPopup: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [maxResults, setMaxResults] = useState(20);
  const [minRating, setMinRating] = useState(0);
  const [maxRating, setMaxRating] = useState(5);
  const [hasPhone, setHasPhone] = useState(false);
  const [hasWebsite, setHasWebsite] = useState(false);
  const [includeReviews, setIncludeReviews] = useState(false);
  const [includePhotos, setIncludePhotos] = useState(false);
  
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [currentStrategy, setCurrentStrategy] = useState('');
  const [businesses, setBusinesses] = useState<BusinessData[]>([]);
  const [stats, setStats] = useState<ExtractionStats | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isContentScriptReady, setIsContentScriptReady] = useState(false);

  useEffect(() => {
    checkContentScriptStatus();
  }, []);

  const checkContentScriptStatus = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tab.id) return;

      const response = await chrome.tabs.sendMessage(tab.id, { type: 'PING' });
      setIsContentScriptReady(response?.success || false);
    } catch (error) {
      setIsContentScriptReady(false);
    }
  };

  const startHybridExtraction = async () => {
    if (!isContentScriptReady) {
      setError('Content script not ready. Please refresh the Google Maps page.');
      return;
    }

    setIsExtracting(true);
    setError(null);
    setSuccess(null);
    setExtractionProgress(0);
    setCurrentStrategy('Initializing...');

    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tab.id) throw new Error('No active tab found');

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setExtractionProgress(prev => Math.min(prev + 10, 90));
      }, 500);

      const strategies = ['Network Interception', 'Internal State Access', 'DOM Scraping'];
      let strategyIndex = 0;
      
      const strategyInterval = setInterval(() => {
        if (strategyIndex < strategies.length) {
          setCurrentStrategy(`Executing ${strategies[strategyIndex]}...`);
          strategyIndex++;
        }
      }, 1500);

      const response = await chrome.tabs.sendMessage(tab.id, {
        type: 'START_EXTRACTION',
        payload: {
          searchParams: {
            query: searchQuery,
            maxResults,
            includeReviews,
            includePhotos
          },
          filterOptions: {
            ratingRange: [minRating, maxRating] as [number, number],
            hasPhone,
            hasWebsite
          }
        }
      });

      clearInterval(progressInterval);
      clearInterval(strategyInterval);
      setExtractionProgress(100);
      setCurrentStrategy('Completed');

      if (response.success) {
        const extractedBusinesses = response.data || [];
        setBusinesses(extractedBusinesses);
        
        // Calculate stats
        const networkResults = extractedBusinesses.filter((b: BusinessData) => 
          b.sources?.includes('Network') || b.source.includes('api')).length;
        const internalResults = extractedBusinesses.filter((b: BusinessData) => 
          b.sources?.includes('Internal') || b.source.includes('internal')).length;
        const domResults = extractedBusinesses.filter((b: BusinessData) => 
          b.sources?.includes('DOM') || b.source.includes('dom')).length;
        
        const avgConfidence = extractedBusinesses.reduce((sum: number, b: BusinessData) => 
          sum + (b.confidence || 0.5), 0) / extractedBusinesses.length;

        setStats({
          totalFound: extractedBusinesses.length,
          networkResults,
          internalResults,
          domResults,
          confidence: avgConfidence,
          executionTime: 0 // Will be calculated from actual extraction
        });

        setSuccess(`Successfully extracted ${extractedBusinesses.length} businesses using hybrid approach!`);
      } else {
        throw new Error(response.error || 'Extraction failed');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsExtracting(false);
      setExtractionProgress(0);
      setCurrentStrategy('');
    }
  };

  const exportToCSV = () => {
    if (businesses.length === 0) return;

    const headers = ['Name', 'Address', 'Category', 'Rating', 'Phone', 'Website', 'Source', 'Confidence', 'Sources'];
    const csvContent = [
      headers.join(','),
      ...businesses.map(business => [
        `"${business.name}"`,
        `"${business.address}"`,
        `"${business.category}"`,
        business.rating || '',
        `"${business.phone || ''}"`,
        `"${business.website || ''}"`,
        `"${business.source}"`,
        business.confidence?.toFixed(2) || '',
        `"${business.sources?.join('; ') || ''}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gmaps-hybrid-extraction-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getStrategyColor = (source: string) => {
    if (source.includes('network') || source.includes('api')) return 'success';
    if (source.includes('internal')) return 'warning';
    if (source.includes('dom')) return 'info';
    return 'default';
  };

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'default';
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  return (
    <Box sx={{ width: 400, maxHeight: 600, overflow: 'auto', p: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <NetworkIcon color="primary" />
        G-Maps Extractor PRO - Hybrid
      </Typography>

      {/* Status Card */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            {isContentScriptReady ? (
              <CheckCircleIcon color="success" fontSize="small" />
            ) : (
              <ErrorIcon color="error" fontSize="small" />
            )}
            <Typography variant="body2">
              Content Script: {isContentScriptReady ? 'Ready' : 'Not Ready'}
            </Typography>
          </Box>
          
          {stats && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Extraction Statistics:</Typography>
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Chip 
                    icon={<NetworkIcon />} 
                    label={`Network: ${stats.networkResults}`} 
                    size="small" 
                    color="success" 
                  />
                </Grid>
                <Grid item xs={6}>
                  <Chip 
                    icon={<StorageIcon />} 
                    label={`Internal: ${stats.internalResults}`} 
                    size="small" 
                    color="warning" 
                  />
                </Grid>
                <Grid item xs={6}>
                  <Chip 
                    icon={<CodeIcon />} 
                    label={`DOM: ${stats.domResults}`} 
                    size="small" 
                    color="info" 
                  />
                </Grid>
                <Grid item xs={6}>
                  <Chip 
                    label={`Total: ${stats.totalFound}`} 
                    size="small" 
                    color="primary" 
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Search Configuration */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1">Search Configuration</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Search Query"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="e.g., restaurants near me"
              size="small"
              fullWidth
            />

            <Box>
              <Typography gutterBottom>Max Results: {maxResults}</Typography>
              <Slider
                value={maxResults}
                onChange={(_, value) => setMaxResults(value as number)}
                min={5}
                max={100}
                step={5}
                marks
                size="small"
              />
            </Box>

            <Box>
              <Typography gutterBottom>Rating Range: {minRating} - {maxRating}</Typography>
              <Slider
                value={[minRating, maxRating]}
                onChange={(_, value) => {
                  const [min, max] = value as number[];
                  setMinRating(min);
                  setMaxRating(max);
                }}
                min={0}
                max={5}
                step={0.1}
                marks
                size="small"
              />
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={<Switch checked={hasPhone} onChange={(e) => setHasPhone(e.target.checked)} />}
                label="Must have phone"
              />
              <FormControlLabel
                control={<Switch checked={hasWebsite} onChange={(e) => setHasWebsite(e.target.checked)} />}
                label="Must have website"
              />
              <FormControlLabel
                control={<Switch checked={includeReviews} onChange={(e) => setIncludeReviews(e.target.checked)} />}
                label="Include reviews"
              />
              <FormControlLabel
                control={<Switch checked={includePhotos} onChange={(e) => setIncludePhotos(e.target.checked)} />}
                label="Include photos"
              />
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Extraction Progress */}
      {isExtracting && (
        <Card sx={{ mt: 2 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <CircularProgress size={20} />
              <Typography variant="body2">{currentStrategy}</Typography>
            </Box>
            <LinearProgress variant="determinate" value={extractionProgress} />
            <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
              {extractionProgress}% Complete
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
        <Button
          variant="contained"
          startIcon={<SearchIcon />}
          onClick={startHybridExtraction}
          disabled={isExtracting || !isContentScriptReady}
          fullWidth
        >
          {isExtracting ? 'Extracting...' : 'Start Hybrid Extraction'}
        </Button>
        
        {businesses.length > 0 && (
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={exportToCSV}
            disabled={isExtracting}
          >
            Export CSV
          </Button>
        )}
      </Box>

      {/* Results */}
      {businesses.length > 0 && (
        <Accordion sx={{ mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Extracted Businesses ({businesses.length})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List dense>
              {businesses.slice(0, 10).map((business, index) => (
                <ListItem key={business.id} divider>
                  <ListItemIcon>
                    <Badge 
                      badgeContent={business.sources?.length || 1} 
                      color="primary"
                      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                    >
                      <Chip
                        label={business.source.split('_')[0]}
                        size="small"
                        color={getStrategyColor(business.source)}
                      />
                    </Badge>
                  </ListItemIcon>
                  <ListItemText
                    primary={business.name}
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          {business.address}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                          {business.rating && (
                            <Chip label={`★ ${business.rating}`} size="small" />
                          )}
                          {business.confidence && (
                            <Chip 
                              label={`${(business.confidence * 100).toFixed(0)}%`} 
                              size="small" 
                              color={getConfidenceColor(business.confidence)}
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
              {businesses.length > 10 && (
                <ListItem>
                  <ListItemText
                    primary={`... and ${businesses.length - 10} more businesses`}
                    sx={{ textAlign: 'center', fontStyle: 'italic' }}
                  />
                </ListItem>
              )}
            </List>
          </AccordionDetails>
        </Accordion>
      )}

      {/* Messages */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mt: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Info */}
      <Alert severity="info" sx={{ mt: 2 }}>
        <Typography variant="caption">
          <strong>Hybrid Approach:</strong> Combines Network Interception, Internal State Access, and DOM Scraping for maximum reliability and data quality.
        </Typography>
      </Alert>
    </Box>
  );
};

export default HybridPopup;
