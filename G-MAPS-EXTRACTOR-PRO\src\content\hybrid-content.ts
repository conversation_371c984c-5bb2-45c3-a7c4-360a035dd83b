// Google Maps Extractor Content Script with Hybrid Approach
// Note: Content scripts cannot use ES6 imports in Manifest V3

// Type definitions (inline)
interface BusinessData {
  id: string;
  name: string;
  address: string;
  category: string;
  rating?: number;
  reviewCount?: number;
  phone?: string;
  website?: string;
  email?: string;
  hours?: any[];
  photos?: string[];
  reviews?: Review[];
  placeId?: string;
  coordinates?: { lat: number; lng: number };
  extractedAt: Date;
  source: string;
  confidence?: number;
  sources?: string[];
}

interface Review {
  id: string;
  businessId: string;
  author: string;
  rating: number;
  text: string;
  date?: Date;
  photos?: string[];
}

interface ChromeMessage {
  type: string;
  payload: any;
}

interface SearchParams {
  query: string;
  maxResults: number;
  includeReviews?: boolean;
  includePhotos?: boolean;
}

interface FilterOptions {
  ratingRange: [number, number];
  hasPhone: boolean;
  hasWebsite: boolean;
}

interface ExtractionStrategy {
  name: string;
  method: () => Promise<BusinessData[]>;
  priority: number;
  timeout: number;
}

interface ExtractionResult {
  businesses: BusinessData[];
  strategy: string;
  confidence: number;
  extractedAt: Date;
  errors?: string[];
}

interface APIResponse {
  url: string;
  data: any;
  timestamp: number;
}

// Utility functions
function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

function sanitizeBusinessData(business: BusinessData): BusinessData {
  return {
    ...business,
    name: business.name?.trim() || '',
    address: business.address?.trim() || '',
    phone: business.phone?.trim(),
    website: business.website?.trim(),
    email: business.email?.trim(),
  };
}

function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Hybrid Extractor Class
class HybridExtractor {
  private strategies: ExtractionStrategy[];
  private maxRetries: number = 3;
  private retryDelay: number = 2000;
  private debug: boolean = true;

  constructor() {
    this.strategies = [
      {
        name: 'Network',
        method: this.extractFromNetwork.bind(this),
        priority: 1,
        timeout: 5000
      },
      {
        name: 'Internal',
        method: this.extractFromInternal.bind(this),
        priority: 2,
        timeout: 3000
      },
      {
        name: 'DOM',
        method: this.extractFromDOM.bind(this),
        priority: 3,
        timeout: 10000
      }
    ];
  }

  /**
   * Main extraction method using hybrid approach
   */
  async extractBusinessData(
    searchParams: SearchParams,
    filterOptions: FilterOptions
  ): Promise<BusinessData[]> {
    this.log('🚀 Starting Hybrid Extraction...');
    
    const results: ExtractionResult[] = [];
    const errors: string[] = [];

    // Execute all strategies in parallel for speed
    const strategyPromises = this.strategies.map(async (strategy) => {
      try {
        this.log(`📡 Executing ${strategy.name} strategy...`);
        
        const startTime = Date.now();
        const businesses = await this.executeWithTimeout(
          strategy.method(),
          strategy.timeout
        );
        
        const executionTime = Date.now() - startTime;
        
        if (businesses && businesses.length > 0) {
          const result: ExtractionResult = {
            businesses,
            strategy: strategy.name,
            confidence: this.calculateStrategyConfidence(strategy.name, businesses),
            extractedAt: new Date(),
          };
          
          this.log(`✅ ${strategy.name}: Found ${businesses.length} businesses in ${executionTime}ms`);
          return result;
        } else {
          this.log(`⚠️ ${strategy.name}: No businesses found`);
          return null;
        }
      } catch (error) {
        const errorMsg = `❌ ${strategy.name} failed: ${error.message}`;
        this.log(errorMsg);
        errors.push(errorMsg);
        return null;
      }
    });

    // Wait for all strategies to complete
    const strategyResults = await Promise.allSettled(strategyPromises);
    
    // Collect successful results
    strategyResults.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      }
    });

    if (results.length === 0) {
      throw new Error(`All extraction strategies failed: ${errors.join('; ')}`);
    }

    // Merge and enhance data from all successful strategies
    const mergedBusinesses = this.mergeResults(results);
    
    // Apply filters
    const filteredBusinesses = this.applyFilters(mergedBusinesses, filterOptions);
    
    // Limit results
    const limitedBusinesses = filteredBusinesses.slice(0, searchParams.maxResults || 20);

    this.log(`🎯 Final result: ${limitedBusinesses.length} businesses from ${results.length} strategies`);
    
    return limitedBusinesses;
  }

  /**
   * Extract from intercepted network requests
   */
  private async extractFromNetwork(): Promise<BusinessData[]> {
    this.log('🌐 Starting Network extraction...');

    // Access intercepted API responses from injected script
    const apiResponses = await this.getStoredAPIResponses();
    this.log(`📡 Received ${apiResponses.length} API responses`);

    const businesses: BusinessData[] = [];

    for (const response of apiResponses) {
      try {
        this.log(`Processing API response from: ${response.url}`);
        const extractedBusinesses = this.processAPIResponse(response);
        this.log(`Extracted ${extractedBusinesses.length} businesses from this response`);
        businesses.push(...extractedBusinesses);
      } catch (error) {
        this.log(`Error processing API response: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    const dedupedBusinesses = this.deduplicateBusinesses(businesses);
    this.log(`🎯 Network extraction complete: ${dedupedBusinesses.length} unique businesses`);
    return dedupedBusinesses;
  }

  /**
   * Extract from Google Maps internal state
   */
  private async extractFromInternal(): Promise<BusinessData[]> {
    this.log('🧠 Starting Internal extraction...');

    return new Promise((resolve) => {
      // Request data from injected script
      const messageHandler = (event: MessageEvent) => {
        if (event.source !== window) return;

        if (event.data.type === 'GMAPS_INTERNAL_RESPONSE') {
          window.removeEventListener('message', messageHandler);
          const businesses = event.data.businesses || [];
          this.log(`🎯 Internal extraction complete: ${businesses.length} businesses found`);
          resolve(businesses);
        }
      };

      window.addEventListener('message', messageHandler);

      this.log('📤 Requesting internal data from injected script...');
      // Request internal data extraction
      window.postMessage({
        type: 'GMAPS_INTERNAL_REQUEST'
      }, '*');

      // Timeout after 3 seconds
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        this.log('⏰ Internal extraction timeout - no response from injected script');
        resolve([]);
      }, 3000);
    });
  }

  /**
   * Extract from DOM elements
   */
  private async extractFromDOM(): Promise<BusinessData[]> {
    const businesses: BusinessData[] = [];

    this.log('🔍 Starting DOM extraction...');
    this.log(`Current URL: ${window.location.href}`);
    this.log(`Page title: ${document.title}`);

    // Enhanced selectors for different Google Maps layouts
    const resultSelectors = [
      '[data-result-index]',
      '.Nv2PK',
      '.bfdHYd',
      '.lI9IFe',
      '.VkpGBb',
      '.THOPZb',
      '[role="article"]',
      '.section-result',
      'a[href*="/place/"]',
      '.hfpxzc',
      '.NrDZNb',
      '.qrShPb',
      // Additional 2024 selectors
      '.fontHeadlineSmall',
      '.fontBodyMedium',
      '[jsaction*="pane"]',
      '[data-value="Search"]',
      '.section-listbox',
      '.section-layout',
      '[aria-label*="Results"]'
    ];

    let resultElements: NodeListOf<Element> | null = null;
    let foundSelector = '';

    // Try each selector until we find results
    for (const selector of resultSelectors) {
      const elements = document.querySelectorAll(selector);
      this.log(`Trying selector "${selector}": found ${elements.length} elements`);

      if (elements.length > 0) {
        this.log(`✅ Found ${elements.length} elements with selector: ${selector}`);
        resultElements = elements;
        foundSelector = selector;
        break;
      }
    }

    if (!resultElements || resultElements.length === 0) {
      this.log('⚠️ No result elements found, trying current business view...');

      // Try to extract from current business view
      const currentBusiness = this.extractCurrentBusinessView();
      if (currentBusiness) {
        this.log(`✅ Found current business: ${currentBusiness.name}`);
        businesses.push(currentBusiness);
      } else {
        this.log('❌ No current business found, trying fallback extraction...');

        // Fallback: Try to extract any business-like content
        const fallbackBusinesses = this.extractFallbackBusinesses();
        if (fallbackBusinesses.length > 0) {
          this.log(`✅ Fallback extraction found ${fallbackBusinesses.length} businesses`);
          businesses.push(...fallbackBusinesses);
        } else {
          this.log('❌ No businesses found with any method');

          // Debug: Log some page structure
          this.log('🔍 Page structure debug:');
          this.log(`- Body classes: ${document.body.className}`);
          this.log(`- Main elements: ${document.querySelectorAll('main, #content, .content, [role="main"]').length}`);
          this.log(`- Divs with data attributes: ${document.querySelectorAll('div[data-*]').length}`);
          this.log(`- Links: ${document.querySelectorAll('a').length}`);
          this.log(`- Text content sample: ${document.body.textContent?.substring(0, 200)}...`);
        }
      }

      return businesses;
    }

    this.log(`🔄 Processing ${resultElements.length} elements from selector: ${foundSelector}`);

    // Extract from each result element
    for (let i = 0; i < resultElements.length; i++) {
      try {
        const element = resultElements[i] as HTMLElement;
        this.log(`Processing element ${i + 1}/${resultElements.length}`);

        const business = this.extractBusinessFromElement(element);

        if (business) {
          this.log(`✅ Extracted business: ${business.name}`);
          businesses.push(business);
        } else {
          this.log(`⚠️ No business data from element ${i + 1}`);
        }
      } catch (error) {
        this.log(`❌ Error extracting from element ${i}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    this.log(`🎯 DOM extraction complete: ${businesses.length} businesses found`);
    return businesses;
  }

  /**
   * Extract business data from a single DOM element
   */
  private extractBusinessFromElement(element: HTMLElement): BusinessData | null {
    try {
      const name = this.extractTextFromElement(element, [
        '.qBF1Pd',
        '.fontHeadlineSmall',
        '.fontHeadlineLarge',
        'h3',
        '.section-result-title',
        '[data-value*="directions"]',
        '.DUwDvf.lfPIob',
        '.hfpxzc',
        '.NrDZNb'
      ]);

      if (!name) return null;

      const address = this.extractTextFromElement(element, [
        '.W4Efsd:nth-child(2)',
        '.Io6YTe',
        '.rogA2c',
        '.fontBodyMedium',
        '.section-result-location'
      ]);

      const category = this.extractTextFromElement(element, [
        '.W4Efsd:first-child',
        '.DkEaL',
        '.fontBodySmall',
        '.section-result-details'
      ]);

      const rating = this.extractRatingFromElement(element);
      const phone = this.extractPhoneFromElement(element);
      const website = this.extractWebsiteFromElement(element);

      return {
        id: generateId(),
        name,
        address: address || '',
        category: category || '',
        rating,
        phone: phone || '',
        website: website || '',
        extractedAt: new Date(),
        source: 'dom_extraction',
        confidence: this.calculateBusinessConfidence({ name, address, category, rating, phone, website })
      };
    } catch (error) {
      this.log(`Error extracting business from element: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Extract text from element using multiple selectors
   */
  private extractTextFromElement(element: Element, selectors: string[]): string {
    for (const selector of selectors) {
      const found = element.querySelector(selector);
      if (found?.textContent?.trim()) {
        const text = found.textContent.trim();
        // Filter out common non-business text
        if (this.isValidBusinessText(text)) {
          return text;
        }
      }
    }
    return '';
  }

  /**
   * Validate if text is likely to be business-related
   */
  private isValidBusinessText(text: string): boolean {
    const invalidTexts = [
      'Google Maps', 'Search', 'Directions', 'Route', 'Navigate',
      'Menu', 'Photos', 'Reviews', 'Hours', 'Call', 'Website'
    ];

    return text.length > 1 &&
           text.length < 200 &&
           !invalidTexts.some(invalid => text.includes(invalid));
  }

  /**
   * Extract rating from element
   */
  private extractRatingFromElement(element: HTMLElement): number | undefined {
    const ratingSelectors = [
      '.MW4etd',
      '.ceNzKf',
      '[aria-label*="star"]',
      '.section-result-rating'
    ];

    for (const selector of ratingSelectors) {
      const ratingElement = element.querySelector(selector);
      if (ratingElement?.textContent) {
        const ratingMatch = ratingElement.textContent.match(/(\d+\.?\d*)/);
        if (ratingMatch) {
          const rating = parseFloat(ratingMatch[1]);
          if (rating >= 0 && rating <= 5) {
            return rating;
          }
        }
      }
    }

    return undefined;
  }

  /**
   * Extract phone from element
   */
  private extractPhoneFromElement(element: HTMLElement): string {
    const phoneSelectors = [
      '[data-item-id="phone"]',
      '[aria-label*="phone"]',
      '.section-result-phone'
    ];

    for (const selector of phoneSelectors) {
      const phoneElement = element.querySelector(selector);
      if (phoneElement?.textContent?.trim()) {
        return phoneElement.textContent.trim();
      }
    }

    return '';
  }

  /**
   * Extract website from element
   */
  private extractWebsiteFromElement(element: HTMLElement): string {
    const websiteSelectors = [
      '[data-item-id="authority"]',
      '[aria-label*="website"]',
      'a[href^="http"]'
    ];

    for (const selector of websiteSelectors) {
      const websiteElement = element.querySelector(selector) as HTMLAnchorElement;
      if (websiteElement?.href) {
        return websiteElement.href;
      }
    }

    return '';
  }

  /**
   * Fallback extraction method - tries to find any business-like content
   */
  private extractFallbackBusinesses(): BusinessData[] {
    const businesses: BusinessData[] = [];

    try {
      this.log('🔄 Starting fallback extraction...');

      // Try to find any text that looks like business names
      const allElements = document.querySelectorAll('*');
      const potentialBusinessNames: string[] = [];

      for (const element of allElements) {
        const text = element.textContent?.trim();
        if (text && text.length > 3 && text.length < 100) {
          // Simple heuristic for business names
          if (this.looksLikeBusinessName(text)) {
            potentialBusinessNames.push(text);
          }
        }
      }

      // Create businesses from potential names
      const uniqueNames = [...new Set(potentialBusinessNames)].slice(0, 5); // Limit to 5

      for (const name of uniqueNames) {
        businesses.push({
          id: generateId(),
          name,
          address: '',
          category: 'Unknown',
          extractedAt: new Date(),
          source: 'fallback_extraction',
          confidence: 0.3 // Low confidence for fallback
        });
      }

      this.log(`Fallback extraction found ${businesses.length} potential businesses`);

    } catch (error) {
      this.log(`Fallback extraction error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return businesses;
  }

  /**
   * Simple heuristic to check if text looks like a business name
   */
  private looksLikeBusinessName(text: string): boolean {
    // Skip common UI text
    const skipTexts = [
      'google', 'maps', 'search', 'directions', 'route', 'navigate',
      'menu', 'photos', 'reviews', 'hours', 'call', 'website', 'share',
      'save', 'more', 'less', 'show', 'hide', 'close', 'open', 'view',
      'edit', 'delete', 'add', 'remove', 'cancel', 'ok', 'yes', 'no'
    ];

    const lowerText = text.toLowerCase();

    // Skip if contains common UI words
    if (skipTexts.some(skip => lowerText.includes(skip))) {
      return false;
    }

    // Skip if all numbers or special characters
    if (!/[a-zA-Z]/.test(text)) {
      return false;
    }

    // Skip if looks like a URL or email
    if (text.includes('http') || text.includes('@') || text.includes('.com')) {
      return false;
    }

    // Prefer text with capital letters (business names often capitalized)
    if (/[A-Z]/.test(text)) {
      return true;
    }

    return false;
  }

  /**
   * Extract current business view (when viewing a single business)
   */
  private extractCurrentBusinessView(): BusinessData | null {
    try {
      const name = this.extractTextFromElement(document.documentElement, [
        'h1[data-attrid="title"]',
        '.x3AX1-LfntMc-header-title-title',
        '.qrShPb',
        '.DUwDvf.lfPIob'
      ]);

      if (!name) return null;

      const address = this.extractTextFromElement(document.documentElement, [
        '[data-item-id="address"]',
        '.Io6YTe',
        '.rogA2c'
      ]);

      const phone = this.extractTextFromElement(document.documentElement, [
        '[data-item-id="phone"]',
        '[aria-label*="phone"]'
      ]);

      const website = document.querySelector('[data-item-id="authority"] a')?.getAttribute('href') || '';

      return {
        id: generateId(),
        name,
        address: address || '',
        category: '',
        phone: phone || '',
        website,
        extractedAt: new Date(),
        source: 'current_view',
        confidence: 0.9
      };
    } catch (error) {
      this.log(`Error extracting current business view: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Get stored API responses from injected script
   */
  private async getStoredAPIResponses(): Promise<APIResponse[]> {
    return new Promise((resolve) => {
      const messageHandler = (event: MessageEvent) => {
        if (event.source !== window) return;

        if (event.data.type === 'GMAPS_API_RESPONSES') {
          window.removeEventListener('message', messageHandler);
          resolve(event.data.responses || []);
        }
      };

      window.addEventListener('message', messageHandler);

      // Request API responses
      window.postMessage({
        type: 'GMAPS_GET_API_RESPONSES'
      }, '*');

      // Timeout after 2 seconds
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        resolve([]);
      }, 2000);
    });
  }

  /**
   * Process API response to extract business data
   */
  private processAPIResponse(response: APIResponse): BusinessData[] {
    const businesses: BusinessData[] = [];

    try {
      // Handle different API response formats
      const data = response.data;

      // Google Places API format
      if (data.results && Array.isArray(data.results)) {
        data.results.forEach((place: any) => {
          const business = this.convertPlaceToBusinessData(place);
          if (business) businesses.push(business);
        });
      }

      // Search API format
      if (data.d && Array.isArray(data.d)) {
        data.d.forEach((item: any) => {
          if (item[14] && Array.isArray(item[14])) {
            item[14].forEach((place: any) => {
              const business = this.convertSearchResultToBusinessData(place);
              if (business) businesses.push(business);
            });
          }
        });
      }

      // Other potential formats
      if (data.features && Array.isArray(data.features)) {
        data.features.forEach((feature: any) => {
          const business = this.convertFeatureToBusinessData(feature);
          if (business) businesses.push(business);
        });
      }

    } catch (error) {
      this.log(`Error processing API response: ${error.message}`);
    }

    return businesses;
  }

  /**
   * Convert Google Places API result to BusinessData
   */
  private convertPlaceToBusinessData(place: any): BusinessData | null {
    try {
      if (!place.name) return null;

      return {
        id: generateId(),
        name: place.name,
        address: place.formatted_address || place.vicinity || '',
        category: place.types?.[0]?.replace(/_/g, ' ') || '',
        rating: place.rating,
        reviewCount: place.user_ratings_total,
        phone: place.formatted_phone_number || place.international_phone_number || '',
        website: place.website || '',
        placeId: place.place_id,
        coordinates: place.geometry?.location ? {
          lat: place.geometry.location.lat,
          lng: place.geometry.location.lng
        } : undefined,
        extractedAt: new Date(),
        source: 'places_api',
        confidence: 0.95
      };
    } catch (error) {
      this.log(`Error converting place data: ${error.message}`);
      return null;
    }
  }

  /**
   * Convert search result to BusinessData
   */
  private convertSearchResultToBusinessData(result: any): BusinessData | null {
    try {
      // Google Maps search results have complex nested structure
      const name = result[11] || result[0];
      if (!name) return null;

      return {
        id: generateId(),
        name,
        address: result[2] || '',
        category: result[13] || '',
        rating: result[4]?.[7],
        reviewCount: result[4]?.[8],
        phone: result[178]?.[0]?.[0] || '',
        website: result[7]?.[0] || '',
        placeId: result[78],
        extractedAt: new Date(),
        source: 'search_api',
        confidence: 0.9
      };
    } catch (error) {
      this.log(`Error converting search result: ${error.message}`);
      return null;
    }
  }

  /**
   * Convert feature to BusinessData
   */
  private convertFeatureToBusinessData(feature: any): BusinessData | null {
    try {
      const properties = feature.properties;
      if (!properties?.name) return null;

      return {
        id: generateId(),
        name: properties.name,
        address: properties.address || '',
        category: properties.category || '',
        rating: properties.rating,
        phone: properties.phone || '',
        website: properties.website || '',
        extractedAt: new Date(),
        source: 'feature_api',
        confidence: 0.85
      };
    } catch (error) {
      this.log(`Error converting feature data: ${error.message}`);
      return null;
    }
  }

  /**
   * Merge results from multiple strategies
   */
  private mergeResults(results: ExtractionResult[]): BusinessData[] {
    const businessMap = new Map<string, BusinessData>();

    // Sort results by confidence (highest first)
    const sortedResults = results.sort((a, b) => b.confidence - a.confidence);

    for (const result of sortedResults) {
      for (const business of result.businesses) {
        const key = this.generateBusinessKey(business);

        if (!businessMap.has(key)) {
          // New business
          businessMap.set(key, {
            ...business,
            sources: [result.strategy],
            confidence: business.confidence || result.confidence
          });
        } else {
          // Merge with existing business
          const existing = businessMap.get(key)!;
          const merged = this.mergeBusiness(existing, business, result.strategy);
          businessMap.set(key, merged);
        }
      }
    }

    return Array.from(businessMap.values());
  }

  /**
   * Generate unique key for business deduplication
   */
  private generateBusinessKey(business: BusinessData): string {
    const name = business.name.toLowerCase().trim();
    const address = business.address.toLowerCase().trim();

    // Use name + first part of address for matching
    const addressPart = address.split(',')[0] || address.substring(0, 50);

    return `${name}-${addressPart}`;
  }

  /**
   * Merge two business objects
   */
  private mergeBusiness(existing: BusinessData, newBusiness: BusinessData, source: string): BusinessData {
    const merged = { ...existing };

    // Add source
    if (!merged.sources) merged.sources = [];
    if (!merged.sources.includes(source)) {
      merged.sources.push(source);
    }

    // Merge fields (prefer non-empty values)
    if (!merged.address && newBusiness.address) merged.address = newBusiness.address;
    if (!merged.category && newBusiness.category) merged.category = newBusiness.category;
    if (!merged.phone && newBusiness.phone) merged.phone = newBusiness.phone;
    if (!merged.website && newBusiness.website) merged.website = newBusiness.website;
    if (!merged.rating && newBusiness.rating) merged.rating = newBusiness.rating;
    if (!merged.reviewCount && newBusiness.reviewCount) merged.reviewCount = newBusiness.reviewCount;
    if (!merged.placeId && newBusiness.placeId) merged.placeId = newBusiness.placeId;
    if (!merged.coordinates && newBusiness.coordinates) merged.coordinates = newBusiness.coordinates;

    // Update confidence based on number of sources
    merged.confidence = Math.min(
      (merged.confidence || 0.5) + (merged.sources.length * 0.1),
      1.0
    );

    return merged;
  }

  /**
   * Remove duplicate businesses
   */
  private deduplicateBusinesses(businesses: BusinessData[]): BusinessData[] {
    const seen = new Set<string>();
    return businesses.filter(business => {
      const key = this.generateBusinessKey(business);
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  /**
   * Apply filters to businesses
   */
  private applyFilters(businesses: BusinessData[], filters: FilterOptions): BusinessData[] {
    return businesses.filter(business => {
      // Rating filter
      if (business.rating) {
        const [minRating, maxRating] = filters.ratingRange;
        if (business.rating < minRating || business.rating > maxRating) {
          return false;
        }
      }

      // Phone filter
      if (filters.hasPhone && !business.phone) {
        return false;
      }

      // Website filter
      if (filters.hasWebsite && !business.website) {
        return false;
      }

      // Confidence filter (only high-confidence results)
      if (business.confidence && business.confidence < 0.3) {
        return false;
      }

      return true;
    });
  }

  /**
   * Calculate confidence for a strategy based on results
   */
  private calculateStrategyConfidence(strategyName: string, businesses: BusinessData[]): number {
    let baseConfidence = 0.5;

    switch (strategyName) {
      case 'Network':
        baseConfidence = 0.95; // Highest confidence - direct from API
        break;
      case 'Internal':
        baseConfidence = 0.85; // High confidence - internal state
        break;
      case 'DOM':
        baseConfidence = 0.7;  // Lower confidence - DOM parsing
        break;
    }

    // Adjust based on data quality
    const avgDataCompleteness = businesses.reduce((sum, business) => {
      let completeness = 0;
      if (business.name) completeness += 0.3;
      if (business.address) completeness += 0.2;
      if (business.phone) completeness += 0.2;
      if (business.website) completeness += 0.15;
      if (business.rating) completeness += 0.15;
      return sum + completeness;
    }, 0) / businesses.length;

    return Math.min(baseConfidence * avgDataCompleteness, 1.0);
  }

  /**
   * Calculate confidence for individual business
   */
  private calculateBusinessConfidence(business: Partial<BusinessData>): number {
    let confidence = 0.5;

    if (business.name) confidence += 0.3;
    if (business.address) confidence += 0.2;
    if (business.phone) confidence += 0.15;
    if (business.website) confidence += 0.15;
    if (business.rating) confidence += 0.1;
    if (business.category) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error('Operation timed out')), timeout)
      )
    ]);
  }

  private log(message: string): void {
    if (this.debug) {
      console.log(`[HybridExtractor] ${message}`);
    }
  }
}

// Content Script Class
class HybridContentScript {
  private extractor: HybridExtractor;
  private isExtracting: boolean = false;

  constructor() {
    this.extractor = new HybridExtractor();
    this.init();
    this.injectScript();
  }

  private init(): void {
    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((message: ChromeMessage, sender, sendResponse) => {
      console.log('Content script received message:', message.type);
      this.handleMessage(message, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Inject helper script for accessing Google Maps internal APIs
    this.injectHelperScript();

    console.log('Hybrid Google Maps Extractor content script loaded on:', window.location.href);

    // Signal that content script is ready
    setTimeout(() => {
      console.log('Hybrid content script initialization complete');
    }, 1000);
  }

  private async handleMessage(message: ChromeMessage, sendResponse: (response: any) => void): Promise<void> {
    try {
      switch (message.type) {
        case 'PING':
          sendResponse({ success: true, message: 'Hybrid content script is ready' });
          break;
        case 'START_EXTRACTION':
          await this.startExtraction(message.payload, sendResponse);
          break;
        case 'EXTRACT_REVIEWS':
          await this.extractReviews(message.payload, sendResponse);
          break;
        case 'EXTRACT_PHOTOS':
          await this.extractPhotos(message.payload, sendResponse);
          break;
        case 'GET_CURRENT_BUSINESS':
          await this.getCurrentBusiness(sendResponse);
          break;
        case 'CLEAR_CACHE':
          await this.clearCache(sendResponse);
          break;
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Hybrid content script error:', error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Unknown error' });
    }
  }

  private injectScript(): void {
    try {
      // Inject the hybrid extraction script
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('gmaps-injected.iife.js'); // Compiled from gmaps-injected.ts
      script.onload = () => {
        console.log('Hybrid injected script loaded successfully');
        script.remove();
      };
      script.onerror = (error) => {
        console.error('Failed to load hybrid injected script:', error);
      };

      (document.head || document.documentElement).appendChild(script);
    } catch (error) {
      console.error('Failed to inject hybrid script:', error);
    }
  }

  private injectHelperScript(): void {
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('gmaps-injected.iife.js');
    script.onload = () => script.remove();
    (document.head || document.documentElement).appendChild(script);
  }

  private async startExtraction(
    params: { searchParams: SearchParams; filterOptions: FilterOptions },
    sendResponse: (response: any) => void
  ): Promise<void> {
    if (this.isExtracting) {
      sendResponse({ success: false, error: 'Extraction already in progress' });
      return;
    }

    try {
      this.isExtracting = true;

      // Check if we're on Google Maps
      if (!this.isGoogleMapsPage()) {
        throw new Error('Please navigate to Google Maps first');
      }

      // Perform search if query is provided
      if (params.searchParams.query) {
        await this.performSearch(params.searchParams.query);
        await delay(3000); // Wait for results to load
      }

      // Extract business data using hybrid approach
      const businesses = await this.extractor.extractBusinessData(
        params.searchParams,
        params.filterOptions
      );

      sendResponse({ success: true, data: businesses });

      // Send extracted businesses to background script for storage
      chrome.runtime.sendMessage({
        type: 'BUSINESSES_EXTRACTED',
        payload: businesses,
      });

    } catch (error) {
      console.error('Hybrid extraction failed:', error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Extraction failed' });
    } finally {
      this.isExtracting = false;
    }
  }

  private isGoogleMapsPage(): boolean {
    return window.location.hostname.includes('google') &&
           (window.location.pathname.includes('/maps') || window.location.hostname.includes('maps.google'));
  }

  private async performSearch(query: string): Promise<void> {
    console.log('Performing hybrid search for:', query);

    // Enhanced search input selectors
    const searchSelectors = [
      '#searchboxinput',
      'input[aria-label*="Search"]',
      'input[placeholder*="Search"]',
      'input[data-value="Search"]',
      'input[jsaction*="paste"]',
      'input[autocomplete="off"]',
      'form input[type="text"]'
    ];

    let searchInput: HTMLInputElement | null = null;

    for (const selector of searchSelectors) {
      searchInput = document.querySelector(selector) as HTMLInputElement;
      if (searchInput && searchInput.offsetParent !== null) {
        console.log('Found search input with selector:', selector);
        break;
      }
    }

    if (!searchInput) {
      throw new Error('Search input not found. Make sure you are on Google Maps search page.');
    }

    // Enhanced search method
    await this.performAdvancedSearch(searchInput, query);
    console.log('Hybrid search submitted');
  }

  private async performAdvancedSearch(searchInput: HTMLInputElement, query: string): Promise<void> {
    // Clear existing value
    searchInput.value = '';
    searchInput.focus();
    await delay(300);

    // Simulate human typing
    for (let i = 0; i < query.length; i++) {
      searchInput.value = query.substring(0, i + 1);

      // Trigger input events for each character
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      searchInput.dispatchEvent(new Event('keyup', { bubbles: true }));

      await delay(50 + Math.random() * 50); // Random delay between 50-100ms
    }

    await delay(500);

    // Final events
    searchInput.dispatchEvent(new Event('change', { bubbles: true }));
    searchInput.dispatchEvent(new Event('blur', { bubbles: true }));

    await delay(300);

    // Try multiple submit methods
    const submitMethods = [
      () => {
        const searchButton = document.querySelector('#searchbox-searchbutton') as HTMLElement;
        if (searchButton && searchButton.offsetParent !== null) {
          searchButton.click();
          return true;
        }
        return false;
      },
      () => {
        const searchForm = searchInput.closest('form');
        if (searchForm) {
          searchForm.submit();
          return true;
        }
        return false;
      },
      () => {
        searchInput.dispatchEvent(new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        }));
        return true;
      }
    ];

    for (const method of submitMethods) {
      if (method()) {
        console.log('Hybrid search submitted successfully');
        break;
      }
      await delay(200);
    }
  }

  private async extractReviews(params: any, sendResponse: (response: any) => void): Promise<void> {
    try {
      // Implementation for review extraction
      sendResponse({ success: true, data: [] });
    } catch (error) {
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Review extraction failed' });
    }
  }

  private async extractPhotos(params: any, sendResponse: (response: any) => void): Promise<void> {
    try {
      // Implementation for photo extraction
      sendResponse({ success: true, data: [] });
    } catch (error) {
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Photo extraction failed' });
    }
  }

  private async getCurrentBusiness(sendResponse: (response: any) => void): Promise<void> {
    try {
      const currentBusiness = this.extractor['extractCurrentBusinessView']();
      sendResponse({ success: true, data: currentBusiness });
    } catch (error) {
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Failed to get current business' });
    }
  }

  private async clearCache(sendResponse: (response: any) => void): Promise<void> {
    try {
      // Clear injected script cache
      window.postMessage({
        type: 'GMAPS_CLEAR_CACHE'
      }, '*');

      // Clear any local content script cache
      // (Add any local cache clearing logic here if needed)

      console.log('Cache cleared successfully');
      sendResponse({ success: true, message: 'Cache cleared successfully' });
    } catch (error) {
      console.error('Failed to clear cache:', error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Failed to clear cache' });
    }
  }
}

// Initialize the hybrid content script
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new HybridContentScript();
  });
} else {
  new HybridContentScript();
}
