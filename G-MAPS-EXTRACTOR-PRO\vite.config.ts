import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { copyFileSync, existsSync, writeFileSync, readFileSync } from 'fs';
import { build } from 'vite';

export default defineConfig({
  plugins: [
    react(),
    // Custom plugin to copy static files, create HTML files, and build injected script
    {
      name: 'copy-static-files-and-build-injected',
      async writeBundle() {
        // Copy manifest.json and icons
        const staticFiles = [
          'static/manifest.json',
          'static/icon16.png',
          'static/icon48.png',
          'static/icon128.png',
        ];

        staticFiles.forEach(file => {
          if (existsSync(file)) {
            const filename = file.split('/').pop();
            copyFileSync(file, `dist/${filename}`);
          }
        });

        // Build injected script separately with IIFE format to avoid CSP issues
        try {
          await build({
            configFile: false,
            build: {
              lib: {
                entry: resolve(__dirname, 'src/injected.ts'),
                name: 'GMapsExtractorInjected',
                fileName: 'injected',
                formats: ['iife']
              },
              outDir: 'dist',
              emptyOutDir: false,
              minify: false,
              sourcemap: false,
            },
            define: {
              __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
            }
          });
          console.log('✅ Injected script built successfully');
        } catch (error) {
          console.error('❌ Failed to build injected script:', error);
        }

        // Build internal API script separately
        try {
          await build({
            configFile: false,
            build: {
              lib: {
                entry: resolve(__dirname, 'src/injected/internal-api.ts'),
                name: 'GMapsInternalAPI',
                fileName: 'internal-api',
                formats: ['iife']
              },
              outDir: 'dist',
              emptyOutDir: false,
              minify: false,
              sourcemap: false,
            },
            define: {
              __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
            }
          });
          console.log('✅ Internal API script built successfully');
        } catch (error) {
          console.error('❌ Failed to build internal API script:', error);
        }

        // Create HTML files for extension pages
        const htmlTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>G Maps Extractor Pro</title>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="{{SCRIPT}}"></script>
</body>
</html>`;

        const htmlFiles = [
          { name: 'popup.html', script: 'popup.js' },
          { name: 'options.html', script: 'options.js' },
          { name: 'sidebar.html', script: 'sidebar.js' },
        ];

        htmlFiles.forEach(({ name, script }) => {
          const html = htmlTemplate.replace('{{SCRIPT}}', script);
          require('fs').writeFileSync(`dist/${name}`, html);
        });
      }
    }
  ],

  build: {
    outDir: 'dist',
    emptyOutDir: true,

    rollupOptions: {
      input: {
        popup: resolve(__dirname, 'src/popup/Popup.tsx'),
        options: resolve(__dirname, 'src/options/Options.tsx'),
        sidebar: resolve(__dirname, 'src/sidebar/Sidebar.tsx'),
        background: resolve(__dirname, 'src/background/background.ts'),
        content: resolve(__dirname, 'src/content/content.ts'),
        // Note: injected.ts will be handled separately
      },

      output: {
        entryFileNames: '[name].js',
        chunkFileNames: '[name].js',
        assetFileNames: '[name].[ext]',
        format: 'es',
        dir: resolve(__dirname, 'dist'),
      },

      external: [],
    },

    // Ensure proper module format for extension
    target: 'esnext',
    minify: false, // Disable minification for debugging

    // Prevent writing to source directories
    write: true,

    // Ensure all output goes to dist only
    sourcemap: false,
  },

  // Development server configuration
  server: {
    port: 3000,
    open: false,
  },

  // Define global constants
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  },

  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
});
