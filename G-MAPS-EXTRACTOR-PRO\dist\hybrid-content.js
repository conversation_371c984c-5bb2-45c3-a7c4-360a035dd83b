function generateId() {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
class HybridExtractor {
  strategies;
  maxRetries = 3;
  retryDelay = 2e3;
  debug = true;
  constructor() {
    this.strategies = [
      {
        name: "Network",
        method: this.extractFromNetwork.bind(this),
        priority: 1,
        timeout: 5e3
      },
      {
        name: "Internal",
        method: this.extractFromInternal.bind(this),
        priority: 2,
        timeout: 3e3
      },
      {
        name: "DOM",
        method: this.extractFromDOM.bind(this),
        priority: 3,
        timeout: 1e4
      }
    ];
  }
  /**
   * Main extraction method using hybrid approach
   */
  async extractBusinessData(searchParams, filterOptions) {
    this.log("🚀 Starting Hybrid Extraction...");
    const results = [];
    const errors = [];
    const strategyPromises = this.strategies.map(async (strategy) => {
      try {
        this.log(`📡 Executing ${strategy.name} strategy...`);
        const startTime = Date.now();
        const businesses = await this.executeWithTimeout(
          strategy.method(),
          strategy.timeout
        );
        const executionTime = Date.now() - startTime;
        if (businesses && businesses.length > 0) {
          const result = {
            businesses,
            strategy: strategy.name,
            confidence: this.calculateStrategyConfidence(strategy.name, businesses),
            extractedAt: /* @__PURE__ */ new Date()
          };
          this.log(`✅ ${strategy.name}: Found ${businesses.length} businesses in ${executionTime}ms`);
          return result;
        } else {
          this.log(`⚠️ ${strategy.name}: No businesses found`);
          return null;
        }
      } catch (error) {
        const errorMsg = `❌ ${strategy.name} failed: ${error.message}`;
        this.log(errorMsg);
        errors.push(errorMsg);
        return null;
      }
    });
    const strategyResults = await Promise.allSettled(strategyPromises);
    strategyResults.forEach((result, index) => {
      if (result.status === "fulfilled" && result.value) {
        results.push(result.value);
      }
    });
    if (results.length === 0) {
      throw new Error(`All extraction strategies failed: ${errors.join("; ")}`);
    }
    const mergedBusinesses = this.mergeResults(results);
    const filteredBusinesses = this.applyFilters(mergedBusinesses, filterOptions);
    const limitedBusinesses = filteredBusinesses.slice(0, searchParams.maxResults || 20);
    this.log(`🎯 Final result: ${limitedBusinesses.length} businesses from ${results.length} strategies`);
    return limitedBusinesses;
  }
  /**
   * Extract from intercepted network requests
   */
  async extractFromNetwork() {
    this.log("🌐 Starting Network extraction...");
    const apiResponses = await this.getStoredAPIResponses();
    this.log(`📡 Received ${apiResponses.length} API responses`);
    const businesses = [];
    for (const response of apiResponses) {
      try {
        this.log(`Processing API response from: ${response.url}`);
        const extractedBusinesses = this.processAPIResponse(response);
        this.log(`Extracted ${extractedBusinesses.length} businesses from this response`);
        businesses.push(...extractedBusinesses);
      } catch (error) {
        this.log(`Error processing API response: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }
    const dedupedBusinesses = this.deduplicateBusinesses(businesses);
    this.log(`🎯 Network extraction complete: ${dedupedBusinesses.length} unique businesses`);
    return dedupedBusinesses;
  }
  /**
   * Extract from Google Maps internal state
   */
  async extractFromInternal() {
    this.log("🧠 Starting Internal extraction...");
    return new Promise((resolve) => {
      const messageHandler = (event) => {
        if (event.source !== window) return;
        if (event.data.type === "GMAPS_INTERNAL_RESPONSE") {
          window.removeEventListener("message", messageHandler);
          const businesses = event.data.businesses || [];
          this.log(`🎯 Internal extraction complete: ${businesses.length} businesses found`);
          resolve(businesses);
        }
      };
      window.addEventListener("message", messageHandler);
      this.log("📤 Requesting internal data from injected script...");
      window.postMessage({
        type: "GMAPS_INTERNAL_REQUEST"
      }, "*");
      setTimeout(() => {
        window.removeEventListener("message", messageHandler);
        this.log("⏰ Internal extraction timeout - no response from injected script");
        resolve([]);
      }, 3e3);
    });
  }
  /**
   * Extract from DOM elements
   */
  async extractFromDOM() {
    const businesses = [];
    this.log("🔍 Starting DOM extraction...");
    this.log(`Current URL: ${window.location.href}`);
    this.log(`Page title: ${document.title}`);
    const resultSelectors = [
      "[data-result-index]",
      ".Nv2PK",
      ".bfdHYd",
      ".lI9IFe",
      ".VkpGBb",
      ".THOPZb",
      '[role="article"]',
      ".section-result",
      'a[href*="/place/"]',
      ".hfpxzc",
      ".NrDZNb",
      ".qrShPb",
      // Additional 2024 selectors
      ".fontHeadlineSmall",
      ".fontBodyMedium",
      '[jsaction*="pane"]',
      '[data-value="Search"]',
      ".section-listbox",
      ".section-layout",
      '[aria-label*="Results"]'
    ];
    let resultElements = null;
    let foundSelector = "";
    for (const selector of resultSelectors) {
      const elements = document.querySelectorAll(selector);
      this.log(`Trying selector "${selector}": found ${elements.length} elements`);
      if (elements.length > 0) {
        this.log(`✅ Found ${elements.length} elements with selector: ${selector}`);
        resultElements = elements;
        foundSelector = selector;
        break;
      }
    }
    if (!resultElements || resultElements.length === 0) {
      this.log("⚠️ No result elements found, trying current business view...");
      const currentBusiness = this.extractCurrentBusinessView();
      if (currentBusiness) {
        this.log(`✅ Found current business: ${currentBusiness.name}`);
        businesses.push(currentBusiness);
      } else {
        this.log("❌ No current business found, trying fallback extraction...");
        const fallbackBusinesses = this.extractFallbackBusinesses();
        if (fallbackBusinesses.length > 0) {
          this.log(`✅ Fallback extraction found ${fallbackBusinesses.length} businesses`);
          businesses.push(...fallbackBusinesses);
        } else {
          this.log("❌ No businesses found with any method");
          this.log("🔍 Page structure debug:");
          this.log(`- Body classes: ${document.body.className}`);
          this.log(`- Main elements: ${document.querySelectorAll('main, #content, .content, [role="main"]').length}`);
          this.log(`- Divs with data attributes: ${document.querySelectorAll("div[data-*]").length}`);
          this.log(`- Links: ${document.querySelectorAll("a").length}`);
          this.log(`- Text content sample: ${document.body.textContent?.substring(0, 200)}...`);
        }
      }
      return businesses;
    }
    this.log(`🔄 Processing ${resultElements.length} elements from selector: ${foundSelector}`);
    for (let i = 0; i < resultElements.length; i++) {
      try {
        const element = resultElements[i];
        this.log(`Processing element ${i + 1}/${resultElements.length}`);
        const business = this.extractBusinessFromElement(element);
        if (business) {
          this.log(`✅ Extracted business: ${business.name}`);
          businesses.push(business);
        } else {
          this.log(`⚠️ No business data from element ${i + 1}`);
        }
      } catch (error) {
        this.log(`❌ Error extracting from element ${i}: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }
    this.log(`🎯 DOM extraction complete: ${businesses.length} businesses found`);
    return businesses;
  }
  /**
   * Extract business data from a single DOM element
   */
  extractBusinessFromElement(element) {
    try {
      const name = this.extractTextFromElement(element, [
        ".qBF1Pd",
        ".fontHeadlineSmall",
        ".fontHeadlineLarge",
        "h3",
        ".section-result-title",
        '[data-value*="directions"]',
        ".DUwDvf.lfPIob",
        ".hfpxzc",
        ".NrDZNb"
      ]);
      if (!name) return null;
      const address = this.extractTextFromElement(element, [
        ".W4Efsd:nth-child(2)",
        ".Io6YTe",
        ".rogA2c",
        ".fontBodyMedium",
        ".section-result-location"
      ]);
      const category = this.extractTextFromElement(element, [
        ".W4Efsd:first-child",
        ".DkEaL",
        ".fontBodySmall",
        ".section-result-details"
      ]);
      const rating = this.extractRatingFromElement(element);
      const phone = this.extractPhoneFromElement(element);
      const website = this.extractWebsiteFromElement(element);
      return {
        id: generateId(),
        name,
        address: address || "",
        category: category || "",
        rating,
        phone: phone || "",
        website: website || "",
        extractedAt: /* @__PURE__ */ new Date(),
        source: "dom_extraction",
        confidence: this.calculateBusinessConfidence({ name, address, category, rating, phone, website })
      };
    } catch (error) {
      this.log(`Error extracting business from element: ${error instanceof Error ? error.message : "Unknown error"}`);
      return null;
    }
  }
  /**
   * Extract text from element using multiple selectors
   */
  extractTextFromElement(element, selectors) {
    for (const selector of selectors) {
      const found = element.querySelector(selector);
      if (found?.textContent?.trim()) {
        const text = found.textContent.trim();
        if (this.isValidBusinessText(text)) {
          return text;
        }
      }
    }
    return "";
  }
  /**
   * Validate if text is likely to be business-related
   */
  isValidBusinessText(text) {
    const invalidTexts = [
      "Google Maps",
      "Search",
      "Directions",
      "Route",
      "Navigate",
      "Menu",
      "Photos",
      "Reviews",
      "Hours",
      "Call",
      "Website"
    ];
    return text.length > 1 && text.length < 200 && !invalidTexts.some((invalid) => text.includes(invalid));
  }
  /**
   * Extract rating from element
   */
  extractRatingFromElement(element) {
    const ratingSelectors = [
      ".MW4etd",
      ".ceNzKf",
      '[aria-label*="star"]',
      ".section-result-rating"
    ];
    for (const selector of ratingSelectors) {
      const ratingElement = element.querySelector(selector);
      if (ratingElement?.textContent) {
        const ratingMatch = ratingElement.textContent.match(/(\d+\.?\d*)/);
        if (ratingMatch) {
          const rating = parseFloat(ratingMatch[1]);
          if (rating >= 0 && rating <= 5) {
            return rating;
          }
        }
      }
    }
    return void 0;
  }
  /**
   * Extract phone from element
   */
  extractPhoneFromElement(element) {
    const phoneSelectors = [
      '[data-item-id="phone"]',
      '[aria-label*="phone"]',
      ".section-result-phone"
    ];
    for (const selector of phoneSelectors) {
      const phoneElement = element.querySelector(selector);
      if (phoneElement?.textContent?.trim()) {
        return phoneElement.textContent.trim();
      }
    }
    return "";
  }
  /**
   * Extract website from element
   */
  extractWebsiteFromElement(element) {
    const websiteSelectors = [
      '[data-item-id="authority"]',
      '[aria-label*="website"]',
      'a[href^="http"]'
    ];
    for (const selector of websiteSelectors) {
      const websiteElement = element.querySelector(selector);
      if (websiteElement?.href) {
        return websiteElement.href;
      }
    }
    return "";
  }
  /**
   * Fallback extraction method - tries to find any business-like content
   */
  extractFallbackBusinesses() {
    const businesses = [];
    try {
      this.log("🔄 Starting fallback extraction...");
      const allElements = document.querySelectorAll("*");
      const potentialBusinessNames = [];
      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];
        const text = element.textContent?.trim();
        if (text && text.length > 3 && text.length < 100) {
          if (this.looksLikeBusinessName(text)) {
            potentialBusinessNames.push(text);
          }
        }
      }
      const uniqueNames = [...new Set(potentialBusinessNames)].slice(0, 5);
      for (const name of uniqueNames) {
        businesses.push({
          id: generateId(),
          name,
          address: "",
          category: "Unknown",
          extractedAt: /* @__PURE__ */ new Date(),
          source: "fallback_extraction",
          confidence: 0.3
          // Low confidence for fallback
        });
      }
      this.log(`Fallback extraction found ${businesses.length} potential businesses`);
    } catch (error) {
      this.log(`Fallback extraction error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
    return businesses;
  }
  /**
   * Simple heuristic to check if text looks like a business name
   */
  looksLikeBusinessName(text) {
    const skipTexts = [
      "google",
      "maps",
      "search",
      "directions",
      "route",
      "navigate",
      "menu",
      "photos",
      "reviews",
      "hours",
      "call",
      "website",
      "share",
      "save",
      "more",
      "less",
      "show",
      "hide",
      "close",
      "open",
      "view",
      "edit",
      "delete",
      "add",
      "remove",
      "cancel",
      "ok",
      "yes",
      "no"
    ];
    const lowerText = text.toLowerCase();
    if (skipTexts.some((skip) => lowerText.includes(skip))) {
      return false;
    }
    if (!/[a-zA-Z]/.test(text)) {
      return false;
    }
    if (text.includes("http") || text.includes("@") || text.includes(".com")) {
      return false;
    }
    if (/[A-Z]/.test(text)) {
      return true;
    }
    return false;
  }
  /**
   * Extract current business view (when viewing a single business)
   */
  extractCurrentBusinessView() {
    try {
      const name = this.extractTextFromElement(document.documentElement, [
        'h1[data-attrid="title"]',
        ".x3AX1-LfntMc-header-title-title",
        ".qrShPb",
        ".DUwDvf.lfPIob"
      ]);
      if (!name) return null;
      const address = this.extractTextFromElement(document.documentElement, [
        '[data-item-id="address"]',
        ".Io6YTe",
        ".rogA2c"
      ]);
      const phone = this.extractTextFromElement(document.documentElement, [
        '[data-item-id="phone"]',
        '[aria-label*="phone"]'
      ]);
      const website = document.querySelector('[data-item-id="authority"] a')?.getAttribute("href") || "";
      return {
        id: generateId(),
        name,
        address: address || "",
        category: "",
        phone: phone || "",
        website,
        extractedAt: /* @__PURE__ */ new Date(),
        source: "current_view",
        confidence: 0.9
      };
    } catch (error) {
      this.log(`Error extracting current business view: ${error instanceof Error ? error.message : "Unknown error"}`);
      return null;
    }
  }
  /**
   * Get stored API responses from injected script
   */
  async getStoredAPIResponses() {
    return new Promise((resolve) => {
      const messageHandler = (event) => {
        if (event.source !== window) return;
        if (event.data.type === "GMAPS_API_RESPONSES") {
          window.removeEventListener("message", messageHandler);
          resolve(event.data.responses || []);
        }
      };
      window.addEventListener("message", messageHandler);
      window.postMessage({
        type: "GMAPS_GET_API_RESPONSES"
      }, "*");
      setTimeout(() => {
        window.removeEventListener("message", messageHandler);
        resolve([]);
      }, 2e3);
    });
  }
  /**
   * Process API response to extract business data
   */
  processAPIResponse(response) {
    const businesses = [];
    try {
      const data = response.data;
      if (data.results && Array.isArray(data.results)) {
        data.results.forEach((place) => {
          const business = this.convertPlaceToBusinessData(place);
          if (business) businesses.push(business);
        });
      }
      if (data.d && Array.isArray(data.d)) {
        data.d.forEach((item) => {
          if (item[14] && Array.isArray(item[14])) {
            item[14].forEach((place) => {
              const business = this.convertSearchResultToBusinessData(place);
              if (business) businesses.push(business);
            });
          }
        });
      }
      if (data.features && Array.isArray(data.features)) {
        data.features.forEach((feature) => {
          const business = this.convertFeatureToBusinessData(feature);
          if (business) businesses.push(business);
        });
      }
    } catch (error) {
      this.log(`Error processing API response: ${error.message}`);
    }
    return businesses;
  }
  /**
   * Convert Google Places API result to BusinessData
   */
  convertPlaceToBusinessData(place) {
    try {
      if (!place.name) return null;
      return {
        id: generateId(),
        name: place.name,
        address: place.formatted_address || place.vicinity || "",
        category: place.types?.[0]?.replace(/_/g, " ") || "",
        rating: place.rating,
        reviewCount: place.user_ratings_total,
        phone: place.formatted_phone_number || place.international_phone_number || "",
        website: place.website || "",
        placeId: place.place_id,
        coordinates: place.geometry?.location ? {
          lat: place.geometry.location.lat,
          lng: place.geometry.location.lng
        } : void 0,
        extractedAt: /* @__PURE__ */ new Date(),
        source: "places_api",
        confidence: 0.95
      };
    } catch (error) {
      this.log(`Error converting place data: ${error.message}`);
      return null;
    }
  }
  /**
   * Convert search result to BusinessData
   */
  convertSearchResultToBusinessData(result) {
    try {
      const name = result[11] || result[0];
      if (!name) return null;
      return {
        id: generateId(),
        name,
        address: result[2] || "",
        category: result[13] || "",
        rating: result[4]?.[7],
        reviewCount: result[4]?.[8],
        phone: result[178]?.[0]?.[0] || "",
        website: result[7]?.[0] || "",
        placeId: result[78],
        extractedAt: /* @__PURE__ */ new Date(),
        source: "search_api",
        confidence: 0.9
      };
    } catch (error) {
      this.log(`Error converting search result: ${error.message}`);
      return null;
    }
  }
  /**
   * Convert feature to BusinessData
   */
  convertFeatureToBusinessData(feature) {
    try {
      const properties = feature.properties;
      if (!properties?.name) return null;
      return {
        id: generateId(),
        name: properties.name,
        address: properties.address || "",
        category: properties.category || "",
        rating: properties.rating,
        phone: properties.phone || "",
        website: properties.website || "",
        extractedAt: /* @__PURE__ */ new Date(),
        source: "feature_api",
        confidence: 0.85
      };
    } catch (error) {
      this.log(`Error converting feature data: ${error.message}`);
      return null;
    }
  }
  /**
   * Merge results from multiple strategies
   */
  mergeResults(results) {
    const businessMap = /* @__PURE__ */ new Map();
    const sortedResults = results.sort((a, b) => b.confidence - a.confidence);
    for (const result of sortedResults) {
      for (const business of result.businesses) {
        const key = this.generateBusinessKey(business);
        if (!businessMap.has(key)) {
          businessMap.set(key, {
            ...business,
            sources: [result.strategy],
            confidence: business.confidence || result.confidence
          });
        } else {
          const existing = businessMap.get(key);
          const merged = this.mergeBusiness(existing, business, result.strategy);
          businessMap.set(key, merged);
        }
      }
    }
    return Array.from(businessMap.values());
  }
  /**
   * Generate unique key for business deduplication
   */
  generateBusinessKey(business) {
    const name = business.name.toLowerCase().trim();
    const address = business.address.toLowerCase().trim();
    const addressPart = address.split(",")[0] || address.substring(0, 50);
    return `${name}-${addressPart}`;
  }
  /**
   * Merge two business objects
   */
  mergeBusiness(existing, newBusiness, source) {
    const merged = { ...existing };
    if (!merged.sources) merged.sources = [];
    if (!merged.sources.includes(source)) {
      merged.sources.push(source);
    }
    if (!merged.address && newBusiness.address) merged.address = newBusiness.address;
    if (!merged.category && newBusiness.category) merged.category = newBusiness.category;
    if (!merged.phone && newBusiness.phone) merged.phone = newBusiness.phone;
    if (!merged.website && newBusiness.website) merged.website = newBusiness.website;
    if (!merged.rating && newBusiness.rating) merged.rating = newBusiness.rating;
    if (!merged.reviewCount && newBusiness.reviewCount) merged.reviewCount = newBusiness.reviewCount;
    if (!merged.placeId && newBusiness.placeId) merged.placeId = newBusiness.placeId;
    if (!merged.coordinates && newBusiness.coordinates) merged.coordinates = newBusiness.coordinates;
    merged.confidence = Math.min(
      (merged.confidence || 0.5) + merged.sources.length * 0.1,
      1
    );
    return merged;
  }
  /**
   * Remove duplicate businesses
   */
  deduplicateBusinesses(businesses) {
    const seen = /* @__PURE__ */ new Set();
    return businesses.filter((business) => {
      const key = this.generateBusinessKey(business);
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }
  /**
   * Apply filters to businesses
   */
  applyFilters(businesses, filters) {
    return businesses.filter((business) => {
      if (business.rating) {
        const [minRating, maxRating] = filters.ratingRange;
        if (business.rating < minRating || business.rating > maxRating) {
          return false;
        }
      }
      if (filters.hasPhone && !business.phone) {
        return false;
      }
      if (filters.hasWebsite && !business.website) {
        return false;
      }
      if (business.confidence && business.confidence < 0.3) {
        return false;
      }
      return true;
    });
  }
  /**
   * Calculate confidence for a strategy based on results
   */
  calculateStrategyConfidence(strategyName, businesses) {
    let baseConfidence = 0.5;
    switch (strategyName) {
      case "Network":
        baseConfidence = 0.95;
        break;
      case "Internal":
        baseConfidence = 0.85;
        break;
      case "DOM":
        baseConfidence = 0.7;
        break;
    }
    const avgDataCompleteness = businesses.reduce((sum, business) => {
      let completeness = 0;
      if (business.name) completeness += 0.3;
      if (business.address) completeness += 0.2;
      if (business.phone) completeness += 0.2;
      if (business.website) completeness += 0.15;
      if (business.rating) completeness += 0.15;
      return sum + completeness;
    }, 0) / businesses.length;
    return Math.min(baseConfidence * avgDataCompleteness, 1);
  }
  /**
   * Calculate confidence for individual business
   */
  calculateBusinessConfidence(business) {
    let confidence = 0.5;
    if (business.name) confidence += 0.3;
    if (business.address) confidence += 0.2;
    if (business.phone) confidence += 0.15;
    if (business.website) confidence += 0.15;
    if (business.rating) confidence += 0.1;
    if (business.category) confidence += 0.1;
    return Math.min(confidence, 1);
  }
  /**
   * Execute function with timeout
   */
  async executeWithTimeout(promise, timeout) {
    return Promise.race([
      promise,
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error("Operation timed out")), timeout)
      )
    ]);
  }
  log(message) {
    if (this.debug) {
      console.log(`[HybridExtractor] ${message}`);
    }
  }
}
class HybridContentScript {
  extractor;
  isExtracting = false;
  constructor() {
    this.extractor = new HybridExtractor();
    this.init();
    this.injectScript();
  }
  init() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log("Content script received message:", message.type);
      this.handleMessage(message, sendResponse);
      return true;
    });
    this.injectHelperScript();
    console.log("Hybrid Google Maps Extractor content script loaded on:", window.location.href);
    setTimeout(() => {
      console.log("Hybrid content script initialization complete");
    }, 1e3);
  }
  async handleMessage(message, sendResponse) {
    try {
      switch (message.type) {
        case "PING":
          sendResponse({ success: true, message: "Hybrid content script is ready" });
          break;
        case "START_EXTRACTION":
          await this.startExtraction(message.payload, sendResponse);
          break;
        case "EXTRACT_REVIEWS":
          await this.extractReviews(message.payload, sendResponse);
          break;
        case "EXTRACT_PHOTOS":
          await this.extractPhotos(message.payload, sendResponse);
          break;
        case "GET_CURRENT_BUSINESS":
          await this.getCurrentBusiness(sendResponse);
          break;
        case "CLEAR_CACHE":
          await this.clearCache(sendResponse);
          break;
        default:
          sendResponse({ success: false, error: "Unknown message type" });
      }
    } catch (error) {
      console.error("Hybrid content script error:", error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Unknown error" });
    }
  }
  injectScript() {
    try {
      const script = document.createElement("script");
      script.src = chrome.runtime.getURL("gmaps-injected.iife.js");
      script.onload = () => {
        console.log("Hybrid injected script loaded successfully");
        script.remove();
      };
      script.onerror = (error) => {
        console.error("Failed to load hybrid injected script:", error);
      };
      (document.head || document.documentElement).appendChild(script);
    } catch (error) {
      console.error("Failed to inject hybrid script:", error);
    }
  }
  injectHelperScript() {
    const script = document.createElement("script");
    script.src = chrome.runtime.getURL("gmaps-injected.iife.js");
    script.onload = () => script.remove();
    (document.head || document.documentElement).appendChild(script);
  }
  async startExtraction(params, sendResponse) {
    if (this.isExtracting) {
      sendResponse({ success: false, error: "Extraction already in progress" });
      return;
    }
    try {
      this.isExtracting = true;
      if (!this.isGoogleMapsPage()) {
        throw new Error("Please navigate to Google Maps first");
      }
      if (params.searchParams.query) {
        await this.performSearch(params.searchParams.query);
        await delay(3e3);
      }
      const businesses = await this.extractor.extractBusinessData(
        params.searchParams,
        params.filterOptions
      );
      sendResponse({ success: true, data: businesses });
      chrome.runtime.sendMessage({
        type: "BUSINESSES_EXTRACTED",
        payload: businesses
      });
    } catch (error) {
      console.error("Hybrid extraction failed:", error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Extraction failed" });
    } finally {
      this.isExtracting = false;
    }
  }
  isGoogleMapsPage() {
    return window.location.hostname.includes("google") && (window.location.pathname.includes("/maps") || window.location.hostname.includes("maps.google"));
  }
  async performSearch(query) {
    console.log("Performing hybrid search for:", query);
    const searchSelectors = [
      "#searchboxinput",
      'input[aria-label*="Search"]',
      'input[placeholder*="Search"]',
      'input[data-value="Search"]',
      'input[jsaction*="paste"]',
      'input[autocomplete="off"]',
      'form input[type="text"]'
    ];
    let searchInput = null;
    for (const selector of searchSelectors) {
      searchInput = document.querySelector(selector);
      if (searchInput && searchInput.offsetParent !== null) {
        console.log("Found search input with selector:", selector);
        break;
      }
    }
    if (!searchInput) {
      throw new Error("Search input not found. Make sure you are on Google Maps search page.");
    }
    await this.performAdvancedSearch(searchInput, query);
    console.log("Hybrid search submitted");
  }
  async performAdvancedSearch(searchInput, query) {
    searchInput.value = "";
    searchInput.focus();
    await delay(300);
    for (let i = 0; i < query.length; i++) {
      searchInput.value = query.substring(0, i + 1);
      searchInput.dispatchEvent(new Event("input", { bubbles: true }));
      searchInput.dispatchEvent(new Event("keyup", { bubbles: true }));
      await delay(50 + Math.random() * 50);
    }
    await delay(500);
    searchInput.dispatchEvent(new Event("change", { bubbles: true }));
    searchInput.dispatchEvent(new Event("blur", { bubbles: true }));
    await delay(300);
    const submitMethods = [
      () => {
        const searchButton = document.querySelector("#searchbox-searchbutton");
        if (searchButton && searchButton.offsetParent !== null) {
          searchButton.click();
          return true;
        }
        return false;
      },
      () => {
        const searchForm = searchInput.closest("form");
        if (searchForm) {
          searchForm.submit();
          return true;
        }
        return false;
      },
      () => {
        searchInput.dispatchEvent(new KeyboardEvent("keydown", {
          key: "Enter",
          code: "Enter",
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        }));
        return true;
      }
    ];
    for (const method of submitMethods) {
      if (method()) {
        console.log("Hybrid search submitted successfully");
        break;
      }
      await delay(200);
    }
  }
  async extractReviews(params, sendResponse) {
    try {
      sendResponse({ success: true, data: [] });
    } catch (error) {
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Review extraction failed" });
    }
  }
  async extractPhotos(params, sendResponse) {
    try {
      sendResponse({ success: true, data: [] });
    } catch (error) {
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Photo extraction failed" });
    }
  }
  async getCurrentBusiness(sendResponse) {
    try {
      const currentBusiness = this.extractor["extractCurrentBusinessView"]();
      sendResponse({ success: true, data: currentBusiness });
    } catch (error) {
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Failed to get current business" });
    }
  }
  async clearCache(sendResponse) {
    try {
      window.postMessage({
        type: "GMAPS_CLEAR_CACHE"
      }, "*");
      console.log("Cache cleared successfully");
      sendResponse({ success: true, message: "Cache cleared successfully" });
    } catch (error) {
      console.error("Failed to clear cache:", error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : "Failed to clear cache" });
    }
  }
}
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    new HybridContentScript();
  });
} else {
  new HybridContentScript();
}
