{"name": "content-disposition", "description": "Create and parse Content-Disposition header", "version": "1.0.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["content-disposition", "http", "rfc6266", "res"], "repository": "jshttp/content-disposition", "dependencies": {"safe-buffer": "5.2.1"}, "devDependencies": {"deep-equal": "1.0.1", "eslint": "7.32.0", "eslint-config-standard": "13.0.1", "eslint-plugin-import": "2.25.3", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^9.2.2", "nyc": "15.1.0"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}