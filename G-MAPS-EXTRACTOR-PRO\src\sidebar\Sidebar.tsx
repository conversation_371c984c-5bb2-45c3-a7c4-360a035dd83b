import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  IconButton,
  Alert,
  CircularProgress,
  ThemeProvider,
  CssBaseline,
  Paper,
  Tabs,
  Tab
} from '@mui/material';
import {
  Business as BusinessIcon,
  RateReview as ReviewIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Close as CloseIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { AppProvider, useApp } from '../context/AppContext';
import { ChromeTabs } from '../utils/chrome';
import BusinessTab from '../components/BusinessTab';
import { lightTheme } from '../theme';
import ReviewsTab from '../components/ReviewsTab';
import SettingsTab from '../components/SettingsTab';


interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel({ children, value, index, ...other }: TabPanelProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`popup-tabpanel-${index}`}
      aria-labelledby={`popup-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
}

interface SidebarContentProps { }

const SidebarContent: React.FC<SidebarContentProps> = () => {
  const { state, setCurrentTab, setError } = useApp();
  const { extractedData: businesses, isLoading, error } = state;

  const handleExport = () => {
    // Export functionality
    const dataToExport = businesses;
    const jsonData = JSON.stringify(dataToExport, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `google-maps-businesses-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleRefresh = () => {
    // Refresh data from current page
    window.location.reload();
  };

  const handleClose = () => {
    // Close sidebar
    const sidebar = document.getElementById('gmaps-extractor-sidebar');
    if (sidebar) {
      sidebar.style.display = 'none';
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading businesses...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  const [isGoogleMaps, setIsGoogleMaps] = useState(false);
  const [checkingTab, setCheckingTab] = useState(true);

  useEffect(() => {
    const checkCurrentTab = async () => {
      try {
        const isGMaps = await ChromeTabs.isGoogleMapsTab();
        setIsGoogleMaps(isGMaps);
      } catch (error) {
        console.error('Failed to check current tab:', error);
        setError('Failed to check current tab');
      } finally {
        setCheckingTab(false);
      }
    };

    checkCurrentTab();
  }, [setError]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    const tabs: ('business' | 'reviews' | 'settings')[] = ['business', 'reviews', 'settings'];
    setCurrentTab(tabs[newValue]);
  };

  const getCurrentTabIndex = () => {
    switch (state.currentTab) {
      case 'business': return 0;
      case 'reviews': return 1;
      case 'settings': return 2;
      default: return 0;
    }
  };

  if (checkingTab) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="sm" sx={{ py: 2, height: '100vh', overflow: 'auto' }}>
      {/* Header */}
      <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" component="h1" sx={{ flexGrow: 1 }}>
            🗺️ G Maps Extractor Pro
          </Typography>
          <Box>
            <IconButton onClick={handleRefresh} size="small" title="Refresh">
              <RefreshIcon />
            </IconButton>
            <IconButton onClick={handleExport} size="small" title="Export Data">
              <DownloadIcon />
            </IconButton>
            <IconButton onClick={handleClose} size="small" title="Close">
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Typography variant="body2" color="text.secondary">
          Found {businesses.length} businesses
        </Typography>
      </Paper>

      {/* Error Alert */}
      {state.error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(undefined)}>
          {state.error}
        </Alert>
      )}

      {/* Google Maps Check */}
      {!isGoogleMaps && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Please navigate to Google Maps to use the extraction features.
        </Alert>
      )}

      {/* Main Tabs */}
      <Card>
        <CardContent sx={{ p: 0 }}>
          <Tabs
            value={getCurrentTabIndex()}
            onChange={handleTabChange}
            aria-label="sidebar tabs"
            variant="fullWidth"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                minHeight: 48,
                fontSize: '0.875rem',
              },
            }}
          >
            <Tab
              icon={<BusinessIcon />}
              label="Business Leads"
              id="sidebar-tab-0"
              aria-controls="sidebar-tabpanel-0"
            />
            <Tab
              icon={<ReviewIcon />}
              label="Reviews & Photos"
              id="sidebar-tab-1"
              aria-controls="sidebar-tabpanel-1"
            />
            <Tab
              icon={<SettingsIcon />}
              label="Settings"
              id="sidebar-tab-2"
              aria-controls="sidebar-tabpanel-2"
            />
          </Tabs>

          {/* Tab Panels */}
          <TabPanel value={getCurrentTabIndex()} index={0}>
            <BusinessTab isGoogleMaps={isGoogleMaps} />
          </TabPanel>
          <TabPanel value={getCurrentTabIndex()} index={1}>
            <ReviewsTab isGoogleMaps={isGoogleMaps} />
          </TabPanel>
          <TabPanel value={getCurrentTabIndex()} index={2}>
            <SettingsTab />
          </TabPanel>
        </CardContent>
      </Card>


      </Container>
  );
};

const Sidebar: React.FC = () => {
  return (
    <ThemeProvider theme={lightTheme}>
      <CssBaseline />
      <AppProvider>
        <SidebarContent />
      </AppProvider>
    </ThemeProvider>
  );
};

// Mount the component
const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(<Sidebar />);

export default Sidebar;
