// Debug script for Google Maps extraction
// Run this in browser console on Google Maps to debug extraction issues

(function() {
  'use strict';

  console.log('🔍 Google Maps Extraction Debug Tool');
  console.log('=====================================');

  // Check page state
  function checkPageState() {
    console.log('\n📍 Page State Check:');
    console.log('URL:', window.location.href);
    console.log('Title:', document.title);
    console.log('Ready State:', document.readyState);
    console.log('Body classes:', document.body.className);
    
    // Check if we're on Google Maps
    const isGoogleMaps = window.location.hostname.includes('google') && 
                        (window.location.pathname.includes('/maps') || 
                         window.location.hostname.includes('maps.google'));
    console.log('Is Google Maps:', isGoogleMaps);
    
    return isGoogleMaps;
  }

  // Test DOM selectors
  function testDOMSelectors() {
    console.log('\n🎯 Testing DOM Selectors:');
    
    const selectors = [
      '[data-result-index]',
      '.Nv2PK',
      '.bfdHYd',
      '.lI9IFe', 
      '.VkpGBb',
      '.THOPZb',
      '[role="article"]',
      '.section-result',
      'a[href*="/place/"]',
      '.hfpxzc',
      '.NrDZNb',
      '.qrShPb',
      '.fontHeadlineSmall',
      '.fontBodyMedium',
      '[jsaction*="pane"]',
      '.section-listbox',
      '.section-layout',
      '[aria-label*="Results"]'
    ];

    let foundElements = 0;
    let bestSelector = '';
    let maxElements = 0;

    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        console.log(`${selector}: ${elements.length} elements`);
        
        if (elements.length > maxElements) {
          maxElements = elements.length;
          bestSelector = selector;
        }
        
        if (elements.length > 0) {
          foundElements++;
          
          // Show sample element content
          if (elements.length <= 3) {
            for (let i = 0; i < elements.length; i++) {
              const text = elements[i].textContent?.trim().substring(0, 100);
              console.log(`  Element ${i + 1}: "${text}..."`);
            }
          }
        }
      } catch (error) {
        console.log(`${selector}: ERROR - ${error.message}`);
      }
    });

    console.log(`\n📊 Summary: ${foundElements}/${selectors.length} selectors found elements`);
    if (bestSelector) {
      console.log(`🏆 Best selector: ${bestSelector} (${maxElements} elements)`);
    }

    return { foundElements, bestSelector, maxElements };
  }

  // Test current business view
  function testCurrentBusinessView() {
    console.log('\n🏢 Testing Current Business View:');
    
    const businessSelectors = [
      'h1[data-attrid="title"]',
      '.x3AX1-LfntMc-header-title-title',
      '.qrShPb',
      '.DUwDvf.lfPIob'
    ];

    businessSelectors.forEach(selector => {
      const element = document.querySelector(selector);
      if (element) {
        console.log(`✅ ${selector}: "${element.textContent?.trim()}"`);
      } else {
        console.log(`❌ ${selector}: not found`);
      }
    });

    // Check for address
    const addressSelectors = [
      '[data-item-id="address"]',
      '.Io6YTe',
      '.rogA2c'
    ];

    console.log('\n📍 Address selectors:');
    addressSelectors.forEach(selector => {
      const element = document.querySelector(selector);
      if (element) {
        console.log(`✅ ${selector}: "${element.textContent?.trim()}"`);
      } else {
        console.log(`❌ ${selector}: not found`);
      }
    });
  }

  // Test injected script communication
  function testInjectedScript() {
    console.log('\n📡 Testing Injected Script Communication:');
    
    let responseReceived = false;
    
    // Listen for responses
    const messageHandler = (event) => {
      if (event.source !== window) return;
      
      if (event.data.type === 'GMAPS_API_RESPONSES') {
        responseReceived = true;
        console.log('✅ Received API responses:', event.data.responses?.length || 0);
        window.removeEventListener('message', messageHandler);
      } else if (event.data.type === 'GMAPS_INTERNAL_RESPONSE') {
        responseReceived = true;
        console.log('✅ Received internal data:', event.data.businesses?.length || 0);
        window.removeEventListener('message', messageHandler);
      }
    };

    window.addEventListener('message', messageHandler);

    // Test API responses
    console.log('📤 Requesting API responses...');
    window.postMessage({ type: 'GMAPS_GET_API_RESPONSES' }, '*');

    setTimeout(() => {
      if (!responseReceived) {
        console.log('❌ No response from injected script (API)');
        
        // Try internal data
        console.log('📤 Requesting internal data...');
        window.postMessage({ type: 'GMAPS_INTERNAL_REQUEST' }, '*');
        
        setTimeout(() => {
          if (!responseReceived) {
            console.log('❌ No response from injected script (Internal)');
            console.log('💡 Injected script may not be loaded or working');
          }
          window.removeEventListener('message', messageHandler);
        }, 2000);
      }
    }, 2000);
  }

  // Test fallback extraction
  function testFallbackExtraction() {
    console.log('\n🔄 Testing Fallback Extraction:');
    
    const allElements = document.querySelectorAll('*');
    const potentialBusinessNames = [];
    
    console.log(`Scanning ${allElements.length} elements...`);
    
    for (let i = 0; i < Math.min(allElements.length, 1000); i++) { // Limit to first 1000
      const element = allElements[i];
      const text = element.textContent?.trim();
      
      if (text && text.length > 3 && text.length < 100) {
        // Simple business name heuristic
        if (looksLikeBusinessName(text)) {
          potentialBusinessNames.push(text);
        }
      }
    }
    
    const uniqueNames = [...new Set(potentialBusinessNames)].slice(0, 10);
    console.log(`Found ${uniqueNames.length} potential business names:`);
    uniqueNames.forEach((name, index) => {
      console.log(`${index + 1}. "${name}"`);
    });
    
    return uniqueNames;
  }

  function looksLikeBusinessName(text) {
    const skipTexts = [
      'google', 'maps', 'search', 'directions', 'route', 'navigate',
      'menu', 'photos', 'reviews', 'hours', 'call', 'website', 'share',
      'save', 'more', 'less', 'show', 'hide', 'close', 'open', 'view'
    ];
    
    const lowerText = text.toLowerCase();
    
    if (skipTexts.some(skip => lowerText.includes(skip))) {
      return false;
    }
    
    if (!/[a-zA-Z]/.test(text)) {
      return false;
    }
    
    if (text.includes('http') || text.includes('@') || text.includes('.com')) {
      return false;
    }
    
    return /[A-Z]/.test(text);
  }

  // Check for Google Maps internal objects
  function checkInternalObjects() {
    console.log('\n🧠 Checking Internal Objects:');
    
    const internalObjects = [
      'APP_INITIALIZATION_STATE',
      '_pageData', 
      'google',
      'gm_authuser_data',
      'GM_STATE',
      'GOOGLE_MAPS_DATA'
    ];

    internalObjects.forEach(objName => {
      const obj = window[objName];
      if (obj) {
        console.log(`✅ ${objName}: exists (${typeof obj})`);
        if (typeof obj === 'object') {
          console.log(`   Keys: ${Object.keys(obj).length}`);
        }
      } else {
        console.log(`❌ ${objName}: not found`);
      }
    });
  }

  // Main debug function
  function runDebug() {
    console.clear();
    console.log('🔍 Google Maps Extraction Debug Tool');
    console.log('=====================================');
    
    if (!checkPageState()) {
      console.log('❌ Not on Google Maps page!');
      return;
    }

    const selectorResults = testDOMSelectors();
    testCurrentBusinessView();
    testInjectedScript();
    checkInternalObjects();
    
    if (selectorResults.foundElements === 0) {
      console.log('\n🔄 No standard selectors found, trying fallback...');
      testFallbackExtraction();
    }

    console.log('\n✅ Debug complete!');
    console.log('\n💡 Recommendations:');
    
    if (selectorResults.foundElements === 0) {
      console.log('- No business elements found with standard selectors');
      console.log('- Try searching for businesses first');
      console.log('- Check if page has loaded completely');
    } else {
      console.log(`- Found elements with ${selectorResults.foundElements} selectors`);
      console.log(`- Best selector: ${selectorResults.bestSelector}`);
    }
  }

  // Export to global scope
  window.GMapsDebug = {
    runDebug,
    checkPageState,
    testDOMSelectors,
    testCurrentBusinessView,
    testInjectedScript,
    testFallbackExtraction,
    checkInternalObjects
  };

  // Auto-run
  runDebug();

})();

console.log('\n🛠️ Debug tool loaded! Use GMapsDebug.runDebug() to run again.');
console.log('Available functions:');
console.log('- GMapsDebug.testDOMSelectors()');
console.log('- GMapsDebug.testCurrentBusinessView()');
console.log('- GMapsDebug.testInjectedScript()');
console.log('- GMapsDebug.checkInternalObjects()');
