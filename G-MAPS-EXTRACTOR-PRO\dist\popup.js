import { R as ReactDOM, j as jsxRuntimeExports, T as ThemeProvider, l as lightTheme, C as CssBaseline, r as reactExports, B as Box, a as CircularProgress, b as Container, c as Typography, A as <PERSON>ert, S as SettingsIcon } from './index.js';
import { A as AppProvider, u as useApp, T as Tabs, a as Tab, B as BusinessIcon, R as ReviewIcon, b as BusinessTab, c as ReviewsTab, S as SettingsTab } from './SettingsTab.js';
import { C as ChromeTabs } from './dataProcessing.js';

function TabPanel({ children, value, index, ...other }) {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      role: "tabpanel",
      hidden: value !== index,
      id: `popup-tabpanel-${index}`,
      "aria-labelledby": `popup-tab-${index}`,
      ...other,
      children: value === index && /* @__PURE__ */ jsxRuntimeExports.jsx(Box, { sx: { p: 2 }, children })
    }
  );
}
function PopupContent() {
  const { state, setCurrentTab, setError } = useApp();
  const [isGoogleMaps, setIsGoogleMaps] = reactExports.useState(false);
  const [checkingTab, setCheckingTab] = reactExports.useState(true);
  reactExports.useEffect(() => {
    const checkCurrentTab = async () => {
      try {
        const isGMaps = await ChromeTabs.isGoogleMapsTab();
        setIsGoogleMaps(isGMaps);
      } catch (error) {
        console.error("Failed to check current tab:", error);
        setError("Failed to check current tab");
      } finally {
        setCheckingTab(false);
      }
    };
    checkCurrentTab();
  }, [setError]);
  const handleTabChange = (event, newValue) => {
    const tabs = ["business", "reviews", "settings"];
    setCurrentTab(tabs[newValue]);
  };
  const getCurrentTabIndex = () => {
    switch (state.currentTab) {
      case "business":
        return 0;
      case "reviews":
        return 1;
      case "settings":
        return 2;
      default:
        return 0;
    }
  };
  if (checkingTab) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", minHeight: "200px", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CircularProgress, {}) });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Container, { maxWidth: "sm", sx: { width: 420, minHeight: 500, p: 0 }, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Box, { sx: { borderBottom: 1, borderColor: "divider", px: 2, py: 1 }, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(Typography, { variant: "h6", component: "h1", sx: { fontWeight: 600 }, children: "G Maps Extractor Pro" }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Typography, { variant: "body2", color: "text.secondary", children: "v2.3.4" })
    ] }),
    state.error && /* @__PURE__ */ jsxRuntimeExports.jsx(Alert, { severity: "error", sx: { m: 2 }, onClose: () => setError(void 0), children: state.error }),
    !isGoogleMaps && /* @__PURE__ */ jsxRuntimeExports.jsx(Alert, { severity: "warning", sx: { m: 2 }, children: "Please navigate to Google Maps to use the extraction features." }),
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Box, { sx: { width: "100%" }, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        Tabs,
        {
          value: getCurrentTabIndex(),
          onChange: handleTabChange,
          "aria-label": "popup tabs",
          variant: "fullWidth",
          sx: {
            borderBottom: 1,
            borderColor: "divider",
            "& .MuiTab-root": {
              minHeight: 48,
              fontSize: "0.875rem"
            }
          },
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tab,
              {
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(BusinessIcon, {}),
                label: "Business Leads",
                id: "popup-tab-0",
                "aria-controls": "popup-tabpanel-0"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tab,
              {
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(ReviewIcon, {}),
                label: "Reviews & Photos",
                id: "popup-tab-1",
                "aria-controls": "popup-tabpanel-1"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tab,
              {
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(SettingsIcon, {}),
                label: "Settings",
                id: "popup-tab-2",
                "aria-controls": "popup-tabpanel-2"
              }
            )
          ]
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TabPanel, { value: getCurrentTabIndex(), index: 0, children: /* @__PURE__ */ jsxRuntimeExports.jsx(BusinessTab, { isGoogleMaps }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TabPanel, { value: getCurrentTabIndex(), index: 1, children: /* @__PURE__ */ jsxRuntimeExports.jsx(ReviewsTab, { isGoogleMaps }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TabPanel, { value: getCurrentTabIndex(), index: 2, children: /* @__PURE__ */ jsxRuntimeExports.jsx(SettingsTab, {}) })
    ] })
  ] });
}
const Popup = () => {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(ThemeProvider, { theme: lightTheme, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(CssBaseline, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsx(AppProvider, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(PopupContent, {}) })
  ] });
};
const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(/* @__PURE__ */ jsxRuntimeExports.jsx(Popup, {}));
