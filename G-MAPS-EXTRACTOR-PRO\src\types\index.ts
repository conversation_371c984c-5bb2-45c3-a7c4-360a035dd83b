// Business Data Types
export interface BusinessData {
  id: string;
  name: string;
  address: string;
  phone?: string;
  website?: string;
  email?: string;
  category: string;
  rating?: number;
  reviewCount?: number;
  priceLevel?: number;
  hours?: BusinessHours[];
  photos?: string[];
  reviews?: Review[];
  coordinates?: {
    lat: number;
    lng: number;
  };
  placeId?: string;
  extractedAt: Date;
  source: 'google_maps';
  // Additional fields for enhanced data extraction
  socialLinks?: string[];
  bookingLink?: string;
  menuLink?: string;
  shareLink?: string;
}

export interface BusinessHours {
  day: string;
  hours: string;
  isOpen: boolean;
}

export interface Review {
  id: string;
  author: string;
  authorUrl?: string;
  authorPhoto?: string;
  rating: number;
  text: string;
  date: Date;
  photos?: string[];
  businessId: string;
}

// Search and Filter Types
export interface SearchParams {
  query: string;
  location: string;
  radius?: number;
  category?: string;
  minRating?: number;
  maxResults?: number;
  includeReviews?: boolean;
  includePhotos?: boolean;
}

export interface FilterOptions {
  categories: string[];
  ratingRange: [number, number];
  reviewCountRange: [number, number];
  priceRange: [number, number];
  hasPhone: boolean;
  hasWebsite: boolean;
  hasEmail: boolean;
  isOpen: boolean;
}

// Export Types
export interface ExportOptions {
  format: 'csv' | 'excel' | 'json';
  fields: string[];
  includeReviews: boolean;
  includePhotos: boolean;
  destination?: 'download' | 'google_drive' | 'hubspot';
}

// User and License Types
export interface User {
  id: string;
  email: string;
  name: string;
  license: LicenseInfo;
  settings: UserSettings;
  createdAt: Date;
}

export interface LicenseInfo {
  type: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'expired' | 'suspended';
  expiresAt?: Date;
  limits: {
    dailyExtractions: number;
    monthlyExtractions: number;
    maxResults: number;
    exportFormats: string[];
    advancedFilters: boolean;
    bulkExtraction: boolean;
  };
  usage: {
    dailyUsed: number;
    monthlyUsed: number;
    lastReset: Date;
  };
}

export interface UserSettings {
  autoSave: boolean;
  defaultExportFormat: 'csv' | 'excel' | 'json';
  defaultFields: string[];
  notifications: boolean;
  theme: 'light' | 'dark' | 'auto';
}

// UI State Types
export interface AppState {
  isLoading: boolean;
  currentTab: 'business' | 'reviews' | 'settings';
  extractedData: BusinessData[];
  selectedBusiness?: BusinessData;
  searchParams: SearchParams;
  filterOptions: FilterOptions;
  user?: User;
  error?: string;
}

// Chrome Extension Types
export interface ChromeMessage {
  type: string;
  payload?: any;
  tabId?: number;
}

export interface ExtractProgress {
  current: number;
  total: number;
  status: 'extracting' | 'completed' | 'error';
  message?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Storage Types
export interface StorageData {
  businesses: BusinessData[];
  user: User;
  settings: UserSettings;
  lastSync: Date;
}
