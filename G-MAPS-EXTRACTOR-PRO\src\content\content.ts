// Google Maps Extractor Content Script
// Note: Content scripts cannot use ES6 imports in Manifest V3

// Type definitions (inline)
interface BusinessData {
  id: string;
  name: string;
  address: string;
  category: string;
  rating?: number;
  reviewCount?: number;
  phone?: string;
  website?: string;
  email?: string;
  hours?: any[];
  photos?: string[];
  reviews?: Review[];
  placeId?: string;
  extractedAt: Date;
  source: string;
}

interface Review {
  id: string;
  businessId: string;
  author: string;
  rating: number;
  text: string;
  date?: Date;
  photos?: string[];
}

interface ChromeMessage {
  type: string;
  payload: any;
}

interface SearchParams {
  query: string;
  maxResults: number;
  includeReviews?: boolean;
  includePhotos?: boolean;
}

interface FilterOptions {
  ratingRange: [number, number];
  hasPhone: boolean;
  hasWebsite: boolean;
}

// Utility functions (inline)
function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

function sanitizeBusinessData(business: BusinessData): BusinessData {
  return {
    ...business,
    name: business.name?.trim() || '',
    address: business.address?.trim() || '',
    phone: business.phone?.trim(),
    website: business.website?.trim(),
    email: business.email?.trim(),
  };
}

class GoogleMapsExtractor {
  private isExtracting = false;
  private extractedBusinesses: BusinessData[] = [];
  private currentSearchParams: SearchParams | null = null;

  constructor() {
    this.init();
    this.injectScript();
  }

  private injectScript(): void {
    try {
      // Inject the enhanced extraction script
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('injected.iife.js'); // Compiled from injected.ts
      script.onload = () => {
        console.log('Injected script loaded successfully');
        script.remove();
      };
      script.onerror = (error) => {
        console.error('Failed to load injected script:', error);
      };

      (document.head || document.documentElement).appendChild(script);
    } catch (error) {
      console.error('Failed to inject script:', error);
    }
  }

  private init() {
    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((message: ChromeMessage, sender, sendResponse) => {
      console.log('Content script received message:', message.type);
      this.handleMessage(message, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Inject helper script for accessing Google Maps internal APIs
    this.injectHelperScript();

    console.log('Google Maps Extractor content script loaded on:', window.location.href);

    // Signal that content script is ready
    setTimeout(() => {
      console.log('Content script initialization complete');
    }, 1000);
  }

  private async handleMessage(message: ChromeMessage, sendResponse: (response: any) => void) {
    try {
      switch (message.type) {
        case 'PING':
          sendResponse({ success: true, message: 'Content script is ready' });
          break;
        case 'START_EXTRACTION':
          await this.startExtraction(message.payload, sendResponse);
          break;
        case 'EXTRACT_REVIEWS':
          await this.extractReviews(message.payload, sendResponse);
          break;
        case 'EXTRACT_PHOTOS':
          await this.extractPhotos(message.payload, sendResponse);
          break;
        case 'GET_CURRENT_BUSINESS':
          await this.getCurrentBusiness(sendResponse);
          break;
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Content script error:', error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Unknown error' });
    }
  }

  private injectHelperScript() {
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('injected.iife.js');
    script.onload = () => script.remove();
    (document.head || document.documentElement).appendChild(script);

    // Also inject internal API access script
    this.injectInternalAPIScript();
  }

  private injectInternalAPIScript() {
    // Use external script file instead of inline script to avoid CSP issues
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('internal-api.iife.js');
    script.onload = () => {
      console.log('Internal API script loaded successfully');
      script.remove();
    };
    script.onerror = (error) => {
      console.error('Failed to load internal API script:', error);
    };

    (document.head || document.documentElement).appendChild(script);
  }

  private async startExtraction(params: { searchParams: SearchParams; filterOptions: FilterOptions }, sendResponse: (response: any) => void) {
    if (this.isExtracting) {
      sendResponse({ success: false, error: 'Extraction already in progress' });
      return;
    }

    try {
      this.isExtracting = true;
      this.currentSearchParams = params.searchParams;
      this.extractedBusinesses = [];

      // Check if we're on Google Maps
      if (!this.isGoogleMapsPage()) {
        throw new Error('Please navigate to Google Maps first');
      }

      // Perform search if query is provided
      if (params.searchParams.query) {
        await this.performSearch(params.searchParams.query);
        await this.waitForSearchResults();
      }

      // Extract business data from search results
      const businesses = await this.extractBusinessesFromResults(params.searchParams, params.filterOptions);

      sendResponse({ success: true, data: businesses });

      // Send extracted businesses to background script for storage
      chrome.runtime.sendMessage({
        type: 'BUSINESSES_EXTRACTED',
        payload: businesses,
      });

    } catch (error) {
      console.error('Extraction failed:', error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : 'Extraction failed' });
    } finally {
      this.isExtracting = false;
    }
  }

  private isGoogleMapsPage(): boolean {
    return window.location.hostname.includes('google') &&
           (window.location.pathname.includes('/maps') || window.location.hostname.includes('maps.google'));
  }

  private async performSearch(query: string): Promise<void> {
    console.log('Performing search for:', query);

    // Enhanced search input selectors based on current Google Maps
    const searchSelectors = [
      '#searchboxinput',
      'input[aria-label*="Search"]',
      'input[placeholder*="Search"]',
      'input[data-value="Search"]',
      'input[jsaction*="paste"]',
      'input[autocomplete="off"]',
      'form input[type="text"]'
    ];

    let searchInput: HTMLInputElement | null = null;

    for (const selector of searchSelectors) {
      searchInput = document.querySelector(selector) as HTMLInputElement;
      if (searchInput && searchInput.offsetParent !== null) {
        console.log('Found search input with selector:', selector);
        break;
      }
    }

    if (!searchInput) {
      throw new Error('Search input not found. Make sure you are on Google Maps search page.');
    }

    // Enhanced search method
    await this.performAdvancedSearch(searchInput, query);
    console.log('Search submitted');
  }

  private async performAdvancedSearch(searchInput: HTMLInputElement, query: string): Promise<void> {
    // Clear existing value
    searchInput.value = '';
    searchInput.focus();
    await this.delay(300);

    // Simulate human typing
    for (let i = 0; i < query.length; i++) {
      searchInput.value = query.substring(0, i + 1);

      // Trigger input events for each character
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      searchInput.dispatchEvent(new Event('keyup', { bubbles: true }));

      await this.delay(50 + Math.random() * 50); // Random delay between 50-100ms
    }

    await this.delay(500);

    // Final events
    searchInput.dispatchEvent(new Event('change', { bubbles: true }));
    searchInput.dispatchEvent(new Event('blur', { bubbles: true }));

    await this.delay(300);

    // Try multiple submit methods
    const submitMethods = [
      () => {
        const searchButton = document.querySelector('#searchbox-searchbutton') as HTMLElement;
        if (searchButton && searchButton.offsetParent !== null) {
          searchButton.click();
          return true;
        }
        return false;
      },
      () => {
        const searchForm = searchInput.closest('form');
        if (searchForm) {
          searchForm.submit();
          return true;
        }
        return false;
      },
      () => {
        searchInput.dispatchEvent(new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        }));
        return true;
      }
    ];

    for (const method of submitMethods) {
      if (method()) {
        console.log('Search submitted successfully');
        break;
      }
      await this.delay(200);
    }
  }

  private async waitForSearchResults(): Promise<void> {
    console.log('Waiting for search results...');
    const maxWait = 20000; // 20 seconds
    const startTime = Date.now();

    // Updated selectors based on actual Google Maps HTML structure
    const resultSelectors = [
      '.Nv2PK.THOPZb.CpccDe', // Main business result container (exact match from HTML)
      '.Nv2PK', // Fallback - main container
      '.bfdHYd.Ppzolf.OFBs3e', // Business content container
      '.lI9IFe', // Inner content container
      'a.hfpxzc', // Business link container
      '[jsaction*="pane.wfvdle"]', // Interactive elements with specific actions
      'div[jsaction*="mouseover:pane"]', // Mouseover interactive elements
      '[data-result-index]', // Traditional result index
      '[role="article"]', // Article role elements
      'a[href*="/place/"]', // Place links
    ];

    let foundResults = false;

    while (Date.now() - startTime < maxWait) {
      // Check for results with multiple strategies
      for (const selector of resultSelectors) {
        const results = document.querySelectorAll(selector);
        if (results.length > 0) {
          console.log(`Found ${results.length} results with selector: ${selector}`);
          foundResults = true;
          break;
        }
      }

      // Also check for specific Google Maps result patterns
      const placeLinks = document.querySelectorAll('a[href*="/place/"]');
      const businessNames = document.querySelectorAll('[data-value*="directions"]');

      if (placeLinks.length > 0 || businessNames.length > 0) {
        console.log(`Found ${placeLinks.length} place links and ${businessNames.length} business elements`);
        foundResults = true;
      }

      if (foundResults) {
        // Wait for results to fully load
        await this.delay(3000);
        return;
      }

      // Check for no results messages
      const noResultsSelectors = [
        '[data-value="No results"]',
        '.section-no-result',
        '.gm-err-container',
        '[aria-label*="No results"]'
      ];

      for (const selector of noResultsSelectors) {
        if (document.querySelector(selector)) {
          throw new Error('No search results found for this query');
        }
      }

      await this.delay(1000);
    }

    throw new Error('Search results did not load in time. Try refreshing the page and searching again.');
  }

  private async extractBusinessesFromResults(searchParams: SearchParams, filterOptions: FilterOptions): Promise<BusinessData[]> {
    const businesses: BusinessData[] = [];
    const maxResults = Math.min(searchParams.maxResults || 20, 100);

    console.log('Starting enhanced business extraction...');

    // First, try to extract using injected script (most reliable)
    const injectedData = await this.extractUsingInjectedScript();
    if (injectedData && injectedData.length > 0) {
      console.log(`Extracted ${injectedData.length} businesses using injected script`);
      businesses.push(...injectedData.slice(0, maxResults));
    }

    // If we don't have enough results, try DOM extraction
    if (businesses.length < maxResults) {
      console.log('Supplementing with DOM extraction...');
      const domBusinesses = await this.extractFromDOM(maxResults - businesses.length, searchParams);
      businesses.push(...domBusinesses);
    }

    // If still no results, try current business view
    if (businesses.length === 0) {
      console.log('No business results found. Trying to extract from current view...');
      const currentBusiness = await this.extractCurrentBusinessView(searchParams);
      if (currentBusiness) {
        businesses.push(currentBusiness);
      }
    }

    // Apply filters
    const filteredBusinesses = this.applyFilters(businesses, filterOptions);

    console.log(`Extraction complete. Found ${filteredBusinesses.length} businesses after filtering.`);
    return filteredBusinesses.slice(0, maxResults);
  }

  private async extractUsingInjectedScript(): Promise<BusinessData[]> {
    return new Promise((resolve) => {
      // Listen for response from injected script
      const messageHandler = (event: MessageEvent) => {
        if (event.source !== window) return;

        if (event.data.type === 'GMAPS_EXTRACT_RESPONSE') {
          window.removeEventListener('message', messageHandler);
          console.log('Received data from injected script:', event.data.data);
          resolve(event.data.data || []);
        }
      };

      window.addEventListener('message', messageHandler);

      // Request extraction from injected script
      window.postMessage({
        type: 'GMAPS_EXTRACT_REQUEST'
      }, '*');

      // Timeout after 5 seconds
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        console.log('Injected script extraction timed out');
        resolve([]);
      }, 5000);
    });
  }

  private async extractFromDOM(maxResults: number, searchParams: SearchParams): Promise<BusinessData[]> {
    const businesses: BusinessData[] = [];
    let processedBusinesses = new Set<string>(); // Track processed businesses to avoid duplicates

    console.log(`Starting extraction with target of ${maxResults} businesses`);

    // Wait for initial search results to load
    await this.waitForSearchResults();

    while (businesses.length < maxResults) {
      console.log(`Current progress: ${businesses.length}/${maxResults} businesses extracted`);

      // Get current visible business elements
      const currentElements = this.findCurrentBusinessElements();

      if (currentElements.length === 0) {
        console.log('No business elements found, search may have failed');
        break;
      }

      console.log(`Found ${currentElements.length} business elements in current view`);

      // Process each business in current view
      let newBusinessesFound = false;
      for (let i = 0; i < currentElements.length && businesses.length < maxResults; i++) {
        const element = currentElements[i] as HTMLElement;

        // Create unique identifier for this business to avoid duplicates
        const businessId = this.createBusinessIdentifier(element);
        if (processedBusinesses.has(businessId)) {
          console.log(`Skipping already processed business: ${businessId}`);
          continue;
        }

        console.log(`Processing business ${businesses.length + 1}/${maxResults}: ${businessId}`);
        processedBusinesses.add(businessId);

        try {
          // Only extract detailed data by clicking into business detail view
          const businessData = await this.extractBusinessByClickingDetail(element, businesses.length + 1, searchParams);

          if (businessData) {
            businesses.push(businessData);
            newBusinessesFound = true;
            console.log(`Successfully extracted: ${businessData.name}`);

            // Send progress update
            chrome.runtime.sendMessage({
              type: 'EXTRACTION_PROGRESS',
              payload: {
                current: businesses.length,
                total: maxResults,
                status: 'extracting',
                business: businessData.name
              },
            });
          }

          // Delay between businesses to mimic human behavior
          await this.delay(3000);
        } catch (error) {
          console.error(`Error processing business ${i + 1}:`, error);
        }
      }

      // If we haven't reached the target and no new businesses were found, try to load more
      if (businesses.length < maxResults) {
        console.log(`Need more businesses (${businesses.length}/${maxResults}), attempting to load more results...`);

        const moreResultsLoaded = await this.loadMoreSearchResults();
        if (!moreResultsLoaded && !newBusinessesFound) {
          console.log('No more results available, stopping extraction');
          break;
        }

        // Wait for new results to load
        await this.delay(3000);
      }
    }

    console.log(`Extraction completed. Found ${businesses.length} businesses.`);
    return businesses;
  }

  // Removed duplicate waitForSearchResults method - using the one defined earlier

  private findCurrentBusinessElements(): Element[] {
    // Updated selectors based on actual Google Maps HTML structure
    const resultSelectors = [
      '.Nv2PK.THOPZb.CpccDe', // Main business result container (exact match from HTML)
      '.Nv2PK', // Fallback - main container
      '.bfdHYd.Ppzolf.OFBs3e', // Business content container
      'a.hfpxzc', // Business link container
      '[jsaction*="pane.wfvdle"]', // Interactive elements with specific actions
      'div[jsaction*="mouseover:pane"]', // Mouseover interactive elements
      '[data-result-index]', // Traditional result index
      '[role="article"]', // Article role elements
      'a[href*="/place/"]', // Place links
    ];

    // Try each selector until we find results
    for (const selector of resultSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`Found ${elements.length} results with selector: ${selector}`);
        return Array.from(elements);
      }
    }

    return [];
  }

  private createBusinessIdentifier(element: HTMLElement): string {
    // Create unique identifier based on business name and address
    const name = this.extractNameFromElement(element) || '';
    const address = this.extractAddressFromElement(element) || '';

    // Use href if available for more unique identification
    const link = element.querySelector('a.hfpxzc') as HTMLAnchorElement;
    const href = link?.href || '';

    if (href) {
      // Extract place ID or unique part from URL
      const placeMatch = href.match(/place\/([^\/]+)/);
      if (placeMatch) {
        return placeMatch[1];
      }
    }

    // Fallback to name + address combination
    return `${name}-${address}`.replace(/\s+/g, '-').toLowerCase();
  }

  private async extractBusinessByClickingDetail(
    element: HTMLElement,
    businessIndex: number,
    searchParams: SearchParams
  ): Promise<BusinessData | null> {
    try {
      console.log(`Extracting business ${businessIndex} by clicking into detail view...`);

      // Find the clickable link in the business element
      const businessLink = element.querySelector('a.hfpxzc') as HTMLAnchorElement;
      if (!businessLink) {
        console.log('No business link found, skipping');
        return null;
      }

      // Store current scroll position
      const currentScrollY = window.scrollY;

      // Click the business link to open detail view
      console.log('Clicking business link to open detail view...');
      businessLink.click();

      // Wait for detail view to load completely
      console.log('Waiting for detail view to load...');
      const detailViewLoaded = await this.waitForDetailViewToLoad();

      if (!detailViewLoaded) {
        console.log('Detail view failed to load, skipping business');
        return null;
      }

      console.log('Detail view loaded successfully, extracting data...');

      // Extract ALL data from detail view (this is where we get complete info)
      const businessData = await this.extractCompleteBusinessData(searchParams);

      // Close the detail view
      console.log('Closing detail view...');
      await this.closeDetailView();

      // Wait for view to close and stabilize
      await this.delay(2000);

      // Restore scroll position to continue with next business
      window.scrollTo(0, currentScrollY);

      if (businessData) {
        console.log(`Successfully extracted complete data: ${businessData.name}`);
        return businessData;
      } else {
        console.log('Failed to extract business data from detail view');
        return null;
      }

    } catch (error) {
      console.error('Error extracting business by clicking detail:', error);

      // Try to close detail view even if extraction failed
      try {
        await this.closeDetailView();
        await this.delay(1000);
      } catch (closeError) {
        console.error('Failed to close detail view:', closeError);
      }

      return null;
    }
  }

  private async extractCompleteBusinessData(searchParams: SearchParams): Promise<BusinessData | null> {
    try {
      console.log('Extracting complete business data from detail view...');

      // Wait for content to be fully loaded
      await this.waitForContentToLoad();

      // Extract basic information with retry
      console.log('Extracting basic business information...');
      const name = await this.extractWithRetry(() => this.extractBusinessName(), 'business name');
      const address = await this.extractWithRetry(() => this.extractBusinessAddress(), 'address');
      const category = await this.extractWithRetry(() => this.extractBusinessCategory(), 'category');
      const rating = await this.extractWithRetry(() => this.extractBusinessRating(), 'rating');
      const reviewCount = await this.extractWithRetry(() => this.extractBusinessReviewCount(), 'review count');

      console.log('Extracting contact information...');
      const phone = await this.extractWithRetry(() => this.extractBusinessPhone(), 'phone');
      const website = await this.extractWithRetry(() => this.extractBusinessWebsite(), 'website');
      const hours = await this.extractWithRetry(() => this.extractBusinessHours(), 'hours');

      // Extract place ID from URL
      const placeId = this.extractPlaceIdFromCurrentUrl();

      // Scroll down to load additional content (social links, etc.)
      console.log('Scrolling to load additional content...');
      await this.scrollToLoadAdditionalContent();

      // Extract additional information that requires scrolling
      console.log('Extracting additional information...');
      const email = await this.extractWithRetry(() => this.extractBusinessEmail(), 'email');
      const socialLinks = await this.extractWithRetry(() => this.extractSocialLinks(), 'social links');
      const bookingLink = await this.extractWithRetry(() => this.extractBookingLink(), 'booking link');
      const menuLink = await this.extractWithRetry(() => this.extractMenuLink(), 'menu link');

      // Click share button to get share link
      console.log('Getting share link...');
      const shareLink = await this.getShareLinkByClickingShareButton();

      // Extract photos and reviews if requested
      const photos = searchParams.includePhotos ? await this.extractWithRetry(() => this.extractBusinessPhotos(), 'photos') : [];
      const reviews = searchParams.includeReviews ? await this.extractWithRetry(() => this.extractBusinessReviews(), 'reviews') : [];

      if (!name) {
        console.log('No business name found, cannot create business data');
        return null;
      }

      const businessData: BusinessData = {
        id: generateId(),
        name,
        address: address || 'Address not available',
        category: category || 'Unknown',
        rating,
        reviewCount,
        phone,
        website,
        email,
        hours: hours || [],
        photos: photos || [],
        reviews: reviews || [],
        placeId,
        extractedAt: new Date(),
        source: 'google_maps',
        // Additional fields for social links, booking, etc.
        socialLinks,
        bookingLink,
        menuLink,
        shareLink
      };

      console.log('Complete business data extracted:', {
        name: businessData.name,
        address: businessData.address,
        phone: businessData.phone,
        website: businessData.website,
        email: businessData.email,
        socialLinks: businessData.socialLinks?.length || 0,
        shareLink: businessData.shareLink ? 'Yes' : 'No',
        hasHours: (businessData.hours?.length || 0) > 0,
        hasPhotos: (businessData.photos?.length || 0) > 0
      });

      return businessData;
    } catch (error) {
      console.error('Failed to extract complete business data:', error);
      return null;
    }
  }

  private async scrollToLoadAdditionalContent(): Promise<void> {
    try {
      console.log('Scrolling to load additional content (social links, etc.)...');

      // Find the scrollable container (usually the detail panel)
      const scrollContainer = document.querySelector('[role="main"]') ||
                             document.querySelector('.m6QErb') ||
                             document.querySelector('.siAUzd') ||
                             document.body;

      if (scrollContainer) {
        // Scroll down in steps to trigger lazy loading
        const scrollHeight = scrollContainer.scrollHeight;
        const steps = 4; // More steps to ensure we load all content
        const stepSize = scrollHeight / steps;

        for (let i = 1; i <= steps; i++) {
          console.log(`Scrolling step ${i}/${steps} to load content...`);
          scrollContainer.scrollTo(0, stepSize * i);
          await this.delay(1500); // Wait for content to load
        }

        // Scroll back to top
        scrollContainer.scrollTo(0, 0);
        await this.delay(1000);
        console.log('Scrolling completed, additional content should be loaded');
      }
    } catch (error) {
      console.error('Error scrolling to load additional content:', error);
    }
  }

  private async getShareLinkByClickingShareButton(): Promise<string | undefined> {
    try {
      console.log('Looking for share button to get share link...');

      // Share button selectors
      const shareButtonSelectors = [
        '[data-value="share"]',
        '[aria-label*="Share"]',
        '[aria-label*="share"]',
        'button[jsaction*="share"]',
        '.share-button',
        '[data-action="share"]'
      ];

      for (const selector of shareButtonSelectors) {
        const shareButton = document.querySelector(selector) as HTMLElement;
        if (shareButton && this.isElementVisible(shareButton)) {
          console.log(`Found share button with selector: ${selector}`);

          // Click the share button
          shareButton.click();
          await this.delay(1000);

          // Try to get the share URL from the current page URL or share dialog
          const shareUrl = this.extractShareUrlFromDialog() || window.location.href;

          // Close share dialog if it opened
          await this.closeShareDialog();

          console.log(`Share link obtained: ${shareUrl}`);
          return shareUrl;
        }
      }

      // Fallback: use current URL as share link
      const currentUrl = window.location.href;
      if (currentUrl.includes('/place/')) {
        console.log(`Using current URL as share link: ${currentUrl}`);
        return currentUrl;
      }

      console.log('No share button found and no valid URL');
      return undefined;
    } catch (error) {
      console.error('Error getting share link:', error);
      return window.location.href; // Fallback to current URL
    }
  }

  private extractShareUrlFromDialog(): string | undefined {
    try {
      // Look for share URL in share dialog
      const shareUrlSelectors = [
        'input[value*="maps.google.com"]',
        'input[value*="/place/"]',
        '.share-url input',
        '[data-share-url]'
      ];

      for (const selector of shareUrlSelectors) {
        const input = document.querySelector(selector) as HTMLInputElement;
        if (input && input.value) {
          return input.value;
        }
      }

      return undefined;
    } catch (error) {
      console.error('Error extracting share URL from dialog:', error);
      return undefined;
    }
  }

  private async closeShareDialog(): Promise<void> {
    try {
      // Try to close share dialog
      const closeSelectors = [
        '[aria-label="Close"]',
        '.close-button',
        '[data-action="close"]'
      ];

      for (const selector of closeSelectors) {
        const closeButton = document.querySelector(selector) as HTMLElement;
        if (closeButton && this.isElementVisible(closeButton)) {
          closeButton.click();
          await this.delay(500);
          break;
        }
      }

      // Press Escape as fallback
      document.dispatchEvent(new KeyboardEvent('keydown', {
        key: 'Escape',
        code: 'Escape',
        keyCode: 27,
        which: 27,
        bubbles: true
      }));
    } catch (error) {
      console.error('Error closing share dialog:', error);
    }
  }

  private async loadMoreSearchResults(): Promise<boolean> {
    try {
      console.log('Attempting to load more search results by scrolling...');

      // Find the scrollable results container
      const scrollContainers = [
        '.m6QErb.DxyBCb.kA9KIf.dS8AEf', // Main results panel
        '[role="main"]', // Main content area
        '.siAUzd', // Results container
        '.section-listbox', // List container
        '.section-scrollbox', // Scroll container
      ];

      let scrollContainer: Element | null = null;
      for (const selector of scrollContainers) {
        const container = document.querySelector(selector);
        if (container) {
          scrollContainer = container;
          console.log(`Found scroll container: ${selector}`);
          break;
        }
      }

      if (!scrollContainer) {
        console.log('No scroll container found, trying window scroll');
        scrollContainer = document.body;
      }

      // Get current number of business elements
      const initialCount = this.findCurrentBusinessElements().length;
      console.log(`Initial business count: ${initialCount}`);

      // Scroll down to trigger loading more results (like real user)
      const scrollSteps = 3;
      const scrollAmount = scrollContainer.scrollHeight / scrollSteps;

      for (let step = 1; step <= scrollSteps; step++) {
        console.log(`Scrolling step ${step}/${scrollSteps}...`);

        if (scrollContainer === document.body) {
          window.scrollTo(0, window.scrollY + scrollAmount);
        } else {
          scrollContainer.scrollTo(0, scrollContainer.scrollTop + scrollAmount);
        }

        // Wait for new content to load (like real user waiting)
        await this.delay(2000);

        // Check if new results appeared
        const currentCount = this.findCurrentBusinessElements().length;
        if (currentCount > initialCount) {
          console.log(`New results loaded: ${currentCount} total (was ${initialCount})`);

          // Scroll back up a bit to see the new results
          if (scrollContainer === document.body) {
            window.scrollTo(0, window.scrollY - scrollAmount / 2);
          } else {
            scrollContainer.scrollTo(0, scrollContainer.scrollTop - scrollAmount / 2);
          }

          await this.delay(1000);
          return true;
        }
      }

      // Check final count
      const finalCount = this.findCurrentBusinessElements().length;
      console.log(`Final business count after scrolling: ${finalCount}`);

      if (finalCount > initialCount) {
        console.log(`Successfully loaded ${finalCount - initialCount} more results`);
        return true;
      } else {
        console.log('No new results loaded after scrolling');
        return false;
      }

    } catch (error) {
      console.error('Error loading more search results:', error);
      return false;
    }
  }

  private applyFilters(businesses: BusinessData[], filterOptions: FilterOptions): BusinessData[] {
    return businesses.filter(business => {
      // Rating filter
      if (business.rating) {
        const [minRating, maxRating] = filterOptions.ratingRange;
        if (business.rating < minRating || business.rating > maxRating) {
          return false;
        }
      }

      // Phone filter
      if (filterOptions.hasPhone && !business.phone) {
        return false;
      }

      // Website filter
      if (filterOptions.hasWebsite && !business.website) {
        return false;
      }

      return true;
    });
  }

  private async extractCurrentBusinessView(searchParams: SearchParams): Promise<BusinessData | null> {
    try {
      console.log('Extracting from current business view...');

      // Extract basic info from current view
      const name = this.extractBusinessName();
      const address = this.extractBusinessAddress();
      const category = this.extractBusinessCategory();
      const rating = this.extractBusinessRating();
      const reviewCount = this.extractBusinessReviewCount();
      const phone = this.extractBusinessPhone();
      const website = this.extractBusinessWebsite();
      const hours = this.extractBusinessHours();
      const photos = searchParams.includePhotos ? this.extractBusinessPhotos() : [];
      const reviews = searchParams.includeReviews ? await this.extractBusinessReviews() : [];

      if (!name) {
        console.log('No business name found in current view');
        return null;
      }

      const business: BusinessData = {
        id: generateId(),
        name,
        address: address || 'Address not available',
        category: category || 'Unknown',
        rating,
        reviewCount,
        phone,
        website,
        hours: hours || [],
        photos: photos || [],
        reviews: reviews || [],
        extractedAt: new Date(),
        source: 'google_maps',
      };

      console.log('Extracted business from current view:', business.name);
      return sanitizeBusinessData(business);
    } catch (error) {
      console.error('Failed to extract from current business view:', error);
      return null;
    }
  }

  private async extractBusinessFromElement(element: HTMLElement, searchParams: SearchParams): Promise<BusinessData | null> {
    try {
      console.log('Extracting business from element...');

      // First try to extract from the element itself (search result)
      const directExtraction = this.extractFromSearchResultElement(element);
      if (directExtraction.name) {
        console.log('Successfully extracted from search result:', directExtraction.name);

        const business: BusinessData = {
          id: generateId(),
          name: directExtraction.name,
          address: directExtraction.address || 'Address not available',
          category: directExtraction.category || 'Unknown',
          rating: directExtraction.rating,
          reviewCount: directExtraction.reviewCount,
          phone: directExtraction.phone,
          website: directExtraction.website,
          hours: [],
          photos: [],
          reviews: [],
          extractedAt: new Date(),
          source: 'google_maps',
        };

        return sanitizeBusinessData(business);
      }

      // Fallback: Click and extract from detail view
      console.log('Clicking on business element for detailed info...');
      element.click();
      await this.delay(3000); // Wait longer for page to load

      // Extract basic info from detail view
      const name = this.extractBusinessName();
      const address = this.extractBusinessAddress();
      const category = this.extractBusinessCategory();
      const rating = this.extractBusinessRating();
      const reviewCount = this.extractBusinessReviewCount();
      const phone = this.extractBusinessPhone();
      const website = this.extractBusinessWebsite();
      const hours = this.extractBusinessHours();
      const photos = searchParams.includePhotos ? this.extractBusinessPhotos() : [];
      const reviews = searchParams.includeReviews ? await this.extractBusinessReviews() : [];

      console.log('Extracted data from detail view:', { name, address, category, rating, reviewCount, phone, website });

      if (!name) {
        console.log('No business name found, skipping...');
        return null;
      }

      const business: BusinessData = {
        id: generateId(),
        name,
        address: address || 'Address not available',
        category: category || 'Unknown',
        rating,
        reviewCount,
        phone,
        website,
        hours: hours || [],
        photos: photos || [],
        reviews: reviews || [],
        extractedAt: new Date(),
        source: 'google_maps',
      };

      return sanitizeBusinessData(business);
    } catch (error) {
      console.error('Failed to extract business data:', error);
      return null;
    }
  }

  private extractFromSearchResultElement(element: HTMLElement): any {
    try {
      console.log('Extracting from search result element...');

      // Method 1: Try to get data from element attributes
      const dataFromAttributes = this.extractFromElementAttributes(element);
      if (dataFromAttributes.name) {
        console.log('Successfully extracted from attributes:', dataFromAttributes);
        return dataFromAttributes;
      }

      // Method 2: DOM scraping with improved selectors (basic info from search results)
      const name = this.extractNameFromElement(element);
      const address = this.extractAddressFromElement(element); // This will be abbreviated
      const category = this.extractCategoryFromElement(element);
      const rating = this.extractRatingFromElement(element);
      const reviewCount = this.extractReviewCountFromElement(element);
      const hours = this.extractHoursFromElement(element);

      console.log('DOM extraction result (search list - abbreviated info):', { name, address, category, rating, reviewCount, hours });

      return { name, address, category, rating, reviewCount, hours };
    } catch (error) {
      console.error('Failed to extract from search result element:', error);
      return {};
    }
  }

  private extractFromElementAttributes(element: HTMLElement): any {
    try {
      // Try to find data attributes or aria-labels
      const ariaLabel = element.getAttribute('aria-label');
      if (ariaLabel) {
        // Parse aria-label for business info
        const nameMatch = ariaLabel.match(/^([^,]+)/);
        const ratingMatch = ariaLabel.match(/(\d+\.?\d*)\s*stars?/i);
        const reviewMatch = ariaLabel.match(/(\d+)\s*reviews?/i);

        if (nameMatch) {
          return {
            name: nameMatch[1].trim(),
            rating: ratingMatch ? parseFloat(ratingMatch[1]) : undefined,
            reviewCount: reviewMatch ? parseInt(reviewMatch[1]) : undefined
          };
        }
      }

      // Try data attributes
      const dataTitle = element.getAttribute('data-title') || element.getAttribute('title');
      if (dataTitle) {
        return { name: dataTitle };
      }

      return {};
    } catch (error) {
      return {};
    }
  }

  private extractNameFromElement(element: HTMLElement): string {
    // Updated selectors based on actual HTML structure
    const selectors = [
      '.qBF1Pd.fontHeadlineSmall', // Exact match from HTML: business name
      '.qBF1Pd',                   // Fallback - business name container
      '.fontHeadlineSmall',        // Headline text
      'h3',                        // Header fallback
      '.DUwDvf',                   // Another name selector
      '[data-value]',              // Data value attribute
      '.fontHeadlineLarge',        // Large headline
    ];

    for (const selector of selectors) {
      const nameEl = element.querySelector(selector);
      if (nameEl?.textContent?.trim()) {
        const name = nameEl.textContent.trim();
        // Filter out non-business names
        if (name.length > 2 && name.length < 100 && !name.includes('Google')) {
          console.log(`Found name with selector ${selector}: ${name}`);
          return name;
        }
      }
    }

    // Fallback: try to get first text content
    const textContent = element.textContent?.trim();
    if (textContent) {
      const firstLine = textContent.split('\n')[0].trim();
      if (firstLine.length > 2 && firstLine.length < 100) {
        console.log(`Found name from text content: ${firstLine}`);
        return firstLine;
      }
    }

    return '';
  }

  private extractAddressFromElement(element: HTMLElement): string {
    // Based on actual HTML structure: address is in nested W4Efsd > W4Efsd > span (last one)
    const selectors = [
      // Exact structure from HTML: look for address in nested spans, skip icons
      '.W4Efsd .W4Efsd span:last-child span', // Address in nested structure (last span)
      '.W4Efsd .W4Efsd span:last-child',      // Address container
      '.W4Efsd span:last-child span',         // Direct address span
      '.W4Efsd span:last-child',              // Address container fallback

      // Alternative selectors
      '.Io6YTe',                 // Alternative address selector
      '.rogA2c',                 // Another address selector
      '.fontBodyMedium',         // Body text
      '.AeaXub'                  // Address container
    ];

    for (const selector of selectors) {
      const addressEl = element.querySelector(selector);
      if (addressEl?.textContent?.trim()) {
        const text = addressEl.textContent.trim();
        // Check if it looks like an address (contains street info)
        if (this.looksLikeAddress(text)) {
          console.log(`Found address with selector ${selector}: ${text} (Note: This is abbreviated address from search results)`);
          return text + ' (abbreviated)'; // Mark as abbreviated
        }
      }
    }

    // Fallback: look for address patterns in W4Efsd structure, skip icons and separators
    const w4Elements = element.querySelectorAll('.W4Efsd .W4Efsd span');
    for (let i = 0; i < w4Elements.length; i++) {
      const span = w4Elements[i];
      const text = span.textContent?.trim();

      // Skip spans with icons, separators, or category-like text
      if (text && !text.includes('·') && !span.querySelector('.google-symbols') &&
          !this.looksLikeCategory(text) && this.looksLikeAddress(text)) {
        console.log(`Found address in nested W4Efsd span: ${text} (abbreviated)`);
        return text + ' (abbreviated)';
      }
    }

    return '';
  }

  private extractCategoryFromElement(element: HTMLElement): string {
    // Based on actual HTML structure: category is in nested W4Efsd > W4Efsd > span > span
    const selectors = [
      // Exact structure from HTML: W4Efsd > W4Efsd > span > span (first one is category)
      '.W4Efsd .W4Efsd span:first-child span', // Category in nested structure
      '.W4Efsd .W4Efsd span:first-child',      // Category container
      '.W4Efsd span:first-child span',         // Direct category span
      '.W4Efsd span:first-child',              // Category container fallback

      // Alternative selectors
      '.DkEaL',                   // Alternative category selector
      '.mgr77e',                  // Business type selector
      '.fontBodySmall',           // Small body text
      '.YhemCb'                   // Another category selector
    ];

    for (const selector of selectors) {
      const categoryEl = element.querySelector(selector);
      if (categoryEl?.textContent?.trim()) {
        const text = categoryEl.textContent.trim();
        // Check if it looks like a category
        if (this.looksLikeCategory(text)) {
          console.log(`Found category with selector ${selector}: ${text}`);
          return text;
        }
      }
    }

    // Fallback: look for category patterns in W4Efsd structure
    const w4Elements = element.querySelectorAll('.W4Efsd .W4Efsd span');
    for (let i = 0; i < w4Elements.length; i++) {
      const span = w4Elements[i];
      const text = span.textContent?.trim();

      // Skip spans with icons or separators
      if (text && !text.includes('·') && !span.querySelector('.google-symbols') && this.looksLikeCategory(text)) {
        console.log(`Found category in nested W4Efsd span: ${text}`);
        return text;
      }
    }

    return '';
  }

  private extractRatingFromElement(element: HTMLElement): number | undefined {
    // Updated selectors based on actual HTML structure
    const selectors = [
      '.MW4etd[aria-hidden="true"]', // Exact match from HTML: rating number
      '.MW4etd',                     // Fallback - rating container
      '.ceNzKf',                     // Alternative rating selector
      '[aria-label*="stars"]',       // Aria label with stars
      'span[role="img"][aria-label*="stars"]', // Rating with role img
    ];

    for (const selector of selectors) {
      const ratingEl = element.querySelector(selector);
      if (ratingEl?.textContent?.trim()) {
        const ratingText = ratingEl.textContent.trim();
        const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
        if (ratingMatch) {
          const rating = parseFloat(ratingMatch[1]);
          if (rating >= 0 && rating <= 5) {
            console.log(`Found rating: ${rating}`);
            return rating;
          }
        }
      }
    }

    return undefined;
  }

  private extractReviewCountFromElement(element: HTMLElement): number | undefined {
    // Updated selectors based on actual HTML structure
    const selectors = [
      '.UY7F9[aria-hidden="true"]', // Exact match from HTML: review count in parentheses
      '.UY7F9',                     // Fallback - review count container
      '.HHrUdb',                    // Alternative review count selector
      '[aria-label*="reviews"]',    // Aria label with reviews
      'span[aria-label*="Reviews"]', // Review count with capital R
    ];

    for (const selector of selectors) {
      const reviewEl = element.querySelector(selector);
      if (reviewEl?.textContent?.trim()) {
        const reviewText = reviewEl.textContent.trim();
        // Match patterns like "(48)", "(471)", etc.
        const reviewMatch = reviewText.match(/\(?([0-9,]+)\)?/);
        if (reviewMatch) {
          const count = parseInt(reviewMatch[1].replace(/,/g, ''));
          if (count > 0) {
            console.log(`Found review count: ${count}`);
            return count;
          }
        }
      }
    }

    return undefined;
  }

  private looksLikeAddress(text: string): boolean {
    return text.length > 10 &&
           !text.match(/^\d+\.\d+/) &&
           !text.match(/^\d+[\d\s\-\(\)]+$/) &&
           !text.includes('★') &&
           !text.includes('•');
  }

  private looksLikeCategory(text: string): boolean {
    return text.length < 50 &&
           text.length > 2 &&
           !text.includes(',') &&
           !text.match(/^\d/) &&
           !text.includes('★') &&
           !this.looksLikeAddress(text);
  }

  private extractBusinessName(): string {
    // Updated selectors based on current Google Maps structure (2024-2025)
    const selectors = [
      // Business detail page selectors
      'h1[data-attrid="title"]',
      'h1.DUwDvf',
      'h1.x3AX1-LfntMc-header-title-title',
      '[data-attrid="title"] h1',
      '.x3AX1-LfntMc-header-title-title',

      // Search results selectors
      '.qBF1Pd', // Primary business name in search results
      '.fontHeadlineSmall', // Alternative headline
      '.fontHeadlineLarge', // Large headline
      '.section-result-title', // Result title
      '.section-result-title-container .section-result-title',

      // Newer selectors (2024-2025)
      '[data-value*="directions"] .qBF1Pd',
      '.hfpxzc .qBF1Pd', // Business name in list
      '.Nv2PK .qBF1Pd', // Another list container
      '.bfdHYd .qBF1Pd', // Alternative container

      // Fallback selectors
      '[data-value="title"]',
      'h1', // Last resort
      'h2', // Alternative header
      'h3', // Alternative header
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const name = element.textContent.trim();
        // Filter out non-business names
        if (name.length > 1 && name.length < 200 && !name.includes('Google Maps')) {
          console.log(`Found business name with selector ${selector}: ${name}`);
          return name;
        }
      }
    }

    console.log('No business name found with any selector');
    return '';
  }

  private extractBusinessAddress(): string {
    // Updated selectors for current Google Maps (2024-2025)
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:address"]',
      '.QSFF4-text',
      '[data-value="Address"]',
      '.Io6YTe', // Primary address selector
      '.rogA2c', // Alternative address selector
      '[data-item-id="address"]',
      '.fontBodyMedium .Io6YTe',

      // Search results selectors
      '.W4Efsd:last-child', // Address in search results (last child)
      '.W4Efsd:nth-child(2)', // Address as second child
      '.W4Efsd:nth-child(3)', // Address as third child
      '.W4Efsd .Io6YTe', // Address within W4Efsd container

      // Newer selectors (2024-2025)
      '.hfpxzc .W4Efsd:nth-child(2)', // Address in business list
      '.Nv2PK .W4Efsd:nth-child(2)', // Address in alternative container
      '.bfdHYd .W4Efsd:nth-child(2)', // Address in another container
      '.section-result-location', // Location in results
      '.section-result-details .W4Efsd', // Details container

      // Additional selectors
      '[jsaction*="address"]',
      '.fontBodyMedium', // General body text
      '.AeaXub', // Address container
      '.LrzXr', // Another address class
      '.W4Efsd[data-value*="address"]', // Data attribute
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const text = element.textContent.trim();
        // Enhanced address validation
        if (this.looksLikeAddress(text)) {
          console.log(`Found address with selector ${selector}: ${text}`);
          return text;
        }
      }
    }

    console.log('No address found with any selector');
    return '';
  }

  private extractBusinessCategory(): string {
    // Updated selectors for current Google Maps (2024-2025)
    const selectors = [
      // Business detail page selectors
      '.DkEaL', // Primary category selector
      '[data-attrid="kc:/location/location:category"]',
      '.fontBodyMedium .DkEaL',
      '.YhemCb', // Alternative category selector
      '.mgr77e', // Business type selector

      // Search results selectors
      '.W4Efsd:first-child', // Category as first child in search results
      '.W4Efsd:nth-child(1)', // Category as first child
      '.fontBodySmall', // Small text category
      '.section-result-category', // Category in results

      // Newer selectors (2024-2025)
      '.hfpxzc .W4Efsd:first-child', // Category in business list
      '.Nv2PK .W4Efsd:first-child', // Category in alternative container
      '.bfdHYd .W4Efsd:first-child', // Category in another container
      '.section-result-details .DkEaL', // Category in details

      // Additional selectors
      '[jsaction*="category"]',
      '.fontBodySmall .DkEaL', // Small category text
      '.W4Efsd[data-value*="category"]', // Data attribute
      '.business-category', // Generic category class
      '.place-category', // Place category
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const text = element.textContent.trim();
        // Enhanced category validation
        if (this.looksLikeCategory(text)) {
          console.log(`Found category with selector ${selector}: ${text}`);
          return text;
        }
      }
    }

    console.log('No category found with any selector');
    return '';
  }

  private extractBusinessRating(): number | undefined {
    const selectors = [
      '.ceNzKf',
      '[data-attrid="kc:/location/location:rating"]',
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const rating = parseFloat(element.textContent.trim());
        if (!isNaN(rating)) {
          return rating;
        }
      }
    }

    return undefined;
  }

  private extractBusinessReviewCount(): number | undefined {
    const selectors = [
      '.HHrUdb',
      '[data-attrid="kc:/location/location:review_count"]',
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element?.textContent?.trim()) {
        const text = element.textContent.trim();
        const match = text.match(/\(([0-9,]+)\)/);
        if (match) {
          return parseInt(match[1].replace(/,/g, ''));
        }
      }
    }

    return undefined;
  }

  private extractBusinessPhone(): string | undefined {
    // Updated selectors for phone numbers (2024-2025)
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:phone"]',
      'a[href^="tel:"]', // Phone links
      '[data-value*="phone"]',
      '[data-item-id="phone"]',

      // Search results and detail selectors
      '.rogA2c[data-value*="phone"]', // Phone in details
      '.W4Efsd[data-value*="phone"]', // Phone in search results
      '.fontBodyMedium[data-value*="phone"]', // Phone in body text

      // Newer selectors (2024-2025)
      '.section-result-phone', // Phone in results
      '.place-phone', // Place phone
      '.business-phone', // Business phone
      '[jsaction*="phone"]', // Phone action

      // Pattern-based selectors (look for phone-like text)
      '.W4Efsd', // Check all W4Efsd for phone patterns
      '.fontBodyMedium', // Check body text for phone patterns
      '.Io6YTe', // Check address-like containers for phone
    ];

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const text = element.textContent?.trim();
        if (text && this.looksLikePhone(text)) {
          console.log(`Found phone with selector ${selector}: ${text}`);
          return text;
        }

        // Check href attribute for tel: links
        const href = (element as HTMLElement).getAttribute('href');
        if (href && href.startsWith('tel:')) {
          const phone = href.replace('tel:', '').trim();
          console.log(`Found phone from tel link: ${phone}`);
          return phone;
        }
      }
    }

    console.log('No phone found with any selector');
    return undefined;
  }

  private extractBusinessWebsite(): string | undefined {
    // Updated selectors for website (2024-2025)
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:website"] a',
      'a[href^="http"]:not([href*="google"])', // External links
      '[data-value*="website"]',
      '[data-item-id="website"]',

      // Search results and detail selectors
      '.rogA2c[data-value*="website"]', // Website in details
      '.W4Efsd[data-value*="website"]', // Website in search results

      // Newer selectors (2024-2025)
      '.section-result-website', // Website in results
      '.place-website', // Place website
      '.business-website', // Business website
      '[jsaction*="website"]', // Website action

      // Link-based selectors
      'a[href*="://"]', // Any external link
      'a[target="_blank"]', // Links opening in new tab
    ];

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const href = (element as HTMLElement).getAttribute('href');
        if (href && this.looksLikeWebsite(href)) {
          console.log(`Found website with selector ${selector}: ${href}`);
          return href;
        }

        // Check text content for URLs
        const text = element.textContent?.trim();
        if (text && this.looksLikeWebsite(text)) {
          console.log(`Found website from text: ${text}`);
          return text;
        }
      }
    }

    console.log('No website found with any selector');
    return undefined;
  }

  private extractBusinessHours(): any[] {
    // Updated selectors based on actual HTML structure
    const selectors = [
      // Business detail page selectors
      '[data-attrid="kc:/location/location:hours"]',
      '.t39EBf', // Hours container
      '.OqCZI', // Hours table

      // Search results selectors - hours in W4Efsd containers
      '.W4Efsd span[style*="color"]', // Hours with color styling (Closed/Open)
      '.W4Efsd', // All W4Efsd containers that might contain hours

      // General selectors
      '.fontBodySmall', // Small body text that might contain hours
      '.hours-info', // Generic hours class
      '[data-value*="hours"]', // Data attribute with hours
    ];

    const hoursInfo: any[] = [];

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];

        // For W4Efsd containers, check if they contain hours patterns
        if (element.classList.contains('W4Efsd')) {
          const fullText = element.textContent?.trim();
          if (fullText && (fullText.includes('Closed') || fullText.includes('Opens') || fullText.includes('AM') || fullText.includes('PM') || fullText.includes('⋅'))) {
            const hoursData = this.parseHoursText(fullText);
            if (hoursData && !hoursInfo.some(h => h.text === hoursData.text)) {
              hoursInfo.push(hoursData);
              console.log(`Found hours in W4Efsd: ${fullText}`);
            }
          }
        } else {
          // For other elements, check text content
          const text = element.textContent?.trim();
          if (text && this.looksLikeHours(text)) {
            const hoursData = this.parseHoursText(text);
            if (hoursData && !hoursInfo.some(h => h.text === hoursData.text)) {
              hoursInfo.push(hoursData);
              console.log(`Found hours: ${text}`);
            }
          }
        }
      }
    }

    console.log(`Extracted ${hoursInfo.length} hours entries`);
    return hoursInfo;
  }

  // Removed duplicate methods - using the enhanced ones defined later in the file

  private async extractReviews(params: any, sendResponse: (response: any) => void) {
    // Implementation for extracting reviews
    sendResponse({ success: true, data: [] });
  }

  private async extractPhotos(params: any, sendResponse: (response: any) => void) {
    // Implementation for extracting photos
    sendResponse({ success: true, data: [] });
  }

  private async getCurrentBusiness(sendResponse: (response: any) => void) {
    // Implementation for getting current business info
    sendResponse({ success: true, data: null });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private looksLikePhone(text: string): boolean {
    // Phone number patterns
    const phonePatterns = [
      /^\+?[\d\s\-\(\)]{10,}$/, // General phone pattern
      /^\(\d{3}\)\s?\d{3}-?\d{4}$/, // (*************
      /^\d{3}-?\d{3}-?\d{4}$/, // ************
      /^\+\d{1,3}\s?\d{3,}/, // International format
    ];

    return phonePatterns.some(pattern => pattern.test(text.trim())) &&
           text.length >= 10 &&
           text.length <= 20;
  }

  private looksLikeWebsite(url: string): boolean {
    try {
      // Check if it's a valid URL
      const urlObj = new URL(url);

      // Exclude Google domains and internal links
      const excludedDomains = [
        'google.com',
        'maps.google.com',
        'gstatic.com',
        'googleapis.com',
        'googleusercontent.com'
      ];

      return !excludedDomains.some(domain => urlObj.hostname.includes(domain)) &&
             (urlObj.protocol === 'http:' || urlObj.protocol === 'https:');
    } catch {
      // If URL parsing fails, check for basic URL patterns
      return /^https?:\/\/[^\s]+\.[^\s]+/.test(url) &&
             !url.includes('google.com') &&
             !url.includes('gstatic.com');
    }
  }

  private looksLikeEmail(text: string): boolean {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(text.trim());
  }

  private looksLikeHours(text: string): boolean {
    // Hours patterns like "9:00 AM - 5:00 PM", "Open 24 hours", etc.
    const hoursPatterns = [
      /\d{1,2}:\d{2}\s?(AM|PM|am|pm)/,
      /\d{1,2}(AM|PM|am|pm)/,
      /(Open|Closed|Hours)/i,
      /\d{1,2}:\d{2}\s?-\s?\d{1,2}:\d{2}/,
      /Opens\s+\d{1,2}/i, // "Opens 10 AM"
      /⋅/, // Contains bullet point (common in hours)
    ];

    return hoursPatterns.some(pattern => pattern.test(text));
  }

  private extractHoursFromElement(element: HTMLElement): any[] {
    // Based on HTML structure: hours are in W4Efsd containers with specific patterns
    const hoursSelectors = [
      '.W4Efsd span[style*="color"]', // Hours with color styling (Closed/Open)
      '.W4Efsd span', // All spans in W4Efsd
      '.fontBodySmall', // Small body text that might contain hours
      '.hours-info', // Generic hours class
      '[data-value*="hours"]', // Data attribute with hours
    ];

    const hoursInfo: any[] = [];

    for (const selector of hoursSelectors) {
      const elements = element.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const el = elements[i];
        const text = el.textContent?.trim();
        if (text && this.looksLikeHours(text)) {
          // Parse hours information
          const hoursData = this.parseHoursText(text);
          if (hoursData) {
            hoursInfo.push(hoursData);
            console.log(`Found hours: ${text}`);
          }
        }
      }
    }

    // Look for hours patterns in parent W4Efsd containers
    const w4Elements = element.querySelectorAll('.W4Efsd');
    for (let i = 0; i < w4Elements.length; i++) {
      const w4Element = w4Elements[i];
      const fullText = w4Element.textContent?.trim();
      if (fullText && (fullText.includes('Closed') || fullText.includes('Opens') || fullText.includes('AM') || fullText.includes('PM'))) {
        const hoursData = this.parseHoursText(fullText);
        if (hoursData && !hoursInfo.some(h => h.text === hoursData.text)) {
          hoursInfo.push(hoursData);
          console.log(`Found hours in W4Efsd: ${fullText}`);
        }
      }
    }

    return hoursInfo;
  }

  private parseHoursText(text: string): any | null {
    // Parse different hours formats
    if (text.includes('Closed') && text.includes('Opens')) {
      // Format: "Closed ⋅ Opens 10 AM"
      const match = text.match(/Closed.*Opens\s+(\d{1,2}(?::\d{2})?\s*(?:AM|PM))/i);
      if (match) {
        return {
          status: 'closed',
          opensAt: match[1],
          text: text
        };
      }
    } else if (text.includes('Open') && !text.includes('Opens')) {
      // Format: "Open ⋅ Closes 9 PM"
      const match = text.match(/Open.*Closes\s+(\d{1,2}(?::\d{2})?\s*(?:AM|PM))/i);
      if (match) {
        return {
          status: 'open',
          closesAt: match[1],
          text: text
        };
      }
    } else if (text.includes('24 hours') || text.includes('Open 24')) {
      return {
        status: 'open_24_hours',
        text: text
      };
    }

    return null;
  }

  private async clickAndExtractDetailedInfo(element: HTMLElement, index: number): Promise<any | null> {
    try {
      console.log(`Clicking business ${index + 1} to extract detailed info...`);

      // Find the clickable link in the business element
      const businessLink = element.querySelector('a.hfpxzc') as HTMLAnchorElement;
      if (!businessLink) {
        console.log('No business link found for detailed extraction');
        return null;
      }

      // Store current scroll position
      const currentScrollY = window.scrollY;

      // Click the business link to open detail view
      console.log('Clicking business link...');
      businessLink.click();

      // Wait for detail view to load and stabilize
      console.log('Waiting for detail view to load...');
      const detailViewLoaded = await this.waitForDetailViewToLoad();

      if (!detailViewLoaded) {
        console.log('Detail view failed to load, skipping detailed extraction');
        return null;
      }

      console.log('Detail view loaded successfully');

      // This method is now replaced by extractCompleteBusinessData
      // which is called from extractBusinessByClickingDetail
      console.log('Detail view opened - extraction handled by extractCompleteBusinessData');
      const detailedInfo = { name: 'Handled by new flow' };

      // Close the detail view by clicking back or pressing escape
      await this.closeDetailView();

      // Wait for view to close and stabilize
      await this.delay(2000);

      // Restore scroll position to continue with next business
      window.scrollTo(0, currentScrollY);

      console.log('Detailed info extraction completed:', detailedInfo?.name || 'No name');
      return detailedInfo;

    } catch (error) {
      console.error('Failed to extract detailed info by click:', error);

      // Try to close detail view even if extraction failed
      try {
        await this.closeDetailView();
        await this.delay(1000);
      } catch (closeError) {
        console.error('Failed to close detail view:', closeError);
      }

      return null;
    }
  }

  private async closeDetailView(): Promise<void> {
    try {
      console.log('Attempting to close detail view...');

      // Method 1: Try to find the exact BUSINESS DETAIL close button (not search close button)
      const closeButtonSelectors = [
        'button.VfPpkd-icon-LgbsSe.yHy1rc.eT1oJ.mN1ivc[aria-label="Close"]', // Exact business detail close button
        'button.VfPpkd-icon-LgbsSe[aria-label="Close"]', // Business detail close button
        '.hWERUb button[aria-label="Close"]', // Close button inside business detail container
        'button[aria-label="Close"].VfPpkd-icon-LgbsSe', // Alternative selector
      ];

      for (const selector of closeButtonSelectors) {
        const closeButton = document.querySelector(selector) as HTMLElement;
        if (closeButton && this.isElementVisible(closeButton)) {
          // Double check this is NOT the search close button
          if (this.isSearchCloseButton(closeButton)) {
            console.log(`Skipping search close button with selector: ${selector}`);
            continue;
          }

          console.log(`Found and clicking business detail close button with selector: ${selector}`);
          closeButton.click();

          // Wait and verify the detail view is closed
          await this.delay(1000);
          if (await this.waitForDetailViewToClose()) {
            console.log('Detail view closed successfully');
            return;
          }
        }
      }

      // Method 2: Try to find back button
      const backButton = document.querySelector('[data-value="back"]') as HTMLElement;
      if (backButton && this.isElementVisible(backButton)) {
        console.log('Clicking back button to close detail view');
        backButton.click();
        await this.delay(1000);
        if (await this.waitForDetailViewToClose()) {
          console.log('Detail view closed via back button');
          return;
        }
      }

      // Method 3: Press Escape key
      console.log('Pressing Escape key to close detail view');
      document.dispatchEvent(new KeyboardEvent('keydown', {
        key: 'Escape',
        code: 'Escape',
        keyCode: 27,
        which: 27,
        bubbles: true
      }));

      await this.delay(1000);
      if (await this.waitForDetailViewToClose()) {
        console.log('Detail view closed via Escape key');
        return;
      }

      // Method 4: Click on the map area to close detail view
      const mapContainer = document.querySelector('#map') as HTMLElement;
      if (mapContainer) {
        console.log('Clicking map to close detail view');
        mapContainer.click();
        await this.delay(1000);
      }

      console.log('Detail view close attempt completed');

    } catch (error) {
      console.error('Error closing detail view:', error);
    }
  }

  private isElementVisible(element: HTMLElement): boolean {
    try {
      const rect = element.getBoundingClientRect();
      const style = window.getComputedStyle(element);

      return (
        rect.width > 0 &&
        rect.height > 0 &&
        style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        style.opacity !== '0'
      );
    } catch (error) {
      return false;
    }
  }

  private isSearchCloseButton(button: HTMLElement): boolean {
    try {
      // Check if this is the search close button based on class names and parent structure

      // Search close button has these characteristics:
      // 1. Has class "yAuNSb vF7Cdb"
      // 2. Parent has class "lSDxNd"
      // 3. Has jsaction="omnibox.clear"

      if (button.classList.contains('yAuNSb') && button.classList.contains('vF7Cdb')) {
        console.log('Detected search close button by class names');
        return true;
      }

      if (button.getAttribute('jsaction')?.includes('omnibox.clear')) {
        console.log('Detected search close button by jsaction');
        return true;
      }

      // Check parent structure
      const parent = button.closest('.lSDxNd');
      if (parent) {
        console.log('Detected search close button by parent structure');
        return true;
      }

      // Business detail close button characteristics:
      // 1. Has class "VfPpkd-icon-LgbsSe yHy1rc eT1oJ mN1ivc"
      // 2. Parent has class "hWERUb"
      // 3. Contains SVG with specific path

      const businessDetailParent = button.closest('.hWERUb');
      if (businessDetailParent && button.classList.contains('VfPpkd-icon-LgbsSe')) {
        console.log('Confirmed business detail close button');
        return false; // This is NOT a search close button
      }

      return false; // Default to not search close button
    } catch (error) {
      console.error('Error checking if search close button:', error);
      return false;
    }
  }

  private async waitForDetailViewToLoad(maxWaitTime: number = 10000): Promise<boolean> {
    console.log('Waiting for detail view to load...');
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      // Check if BUSINESS DETAIL view has loaded (not search interface)
      const detailViewIndicators = [
        'button.VfPpkd-icon-LgbsSe[aria-label="Close"]', // Business detail close button
        '.hWERUb button[aria-label="Close"]', // Close button in business detail container
        '.m6QErb.DxyBCb.kA9KIf.dS8AEf', // Detail panel
        '[data-value="website"]', // Website link (usually only in detail view)
        '[data-value="phone"]', // Phone link (usually only in detail view)
        '.rogA2c', // Contact info container
        '.Io6YTe', // Address container in detail view
      ];

      let detailViewLoaded = false;
      for (const selector of detailViewIndicators) {
        const element = document.querySelector(selector);
        if (element && this.isElementVisible(element as HTMLElement)) {
          detailViewLoaded = true;
          console.log(`Detail view loaded - found indicator: ${selector}`);
          break;
        }
      }

      if (detailViewLoaded) {
        // Wait a bit more for content to stabilize
        await this.delay(1000);
        console.log('Detail view confirmed loaded and stabilized');
        return true;
      }

      await this.delay(200);
    }

    console.log('Detail view failed to load within timeout');
    return false;
  }

  private async waitForDetailViewToClose(maxWaitTime: number = 5000): Promise<boolean> {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      // Check if detail view is still open by looking for BUSINESS DETAIL indicators (not search)
      const detailViewIndicators = [
        'button.VfPpkd-icon-LgbsSe[aria-label="Close"]', // Business detail close button
        '.hWERUb button[aria-label="Close"]', // Close button in business detail container
        '.m6QErb.DxyBCb.kA9KIf.dS8AEf', // Detail panel
        '[data-value="website"]', // Website link (usually only in detail view)
        '[data-value="phone"]', // Phone link (usually only in detail view)
        '.rogA2c', // Contact info container in detail view
      ];

      let detailViewOpen = false;
      for (const selector of detailViewIndicators) {
        const element = document.querySelector(selector);
        if (element && this.isElementVisible(element as HTMLElement)) {
          detailViewOpen = true;
          break;
        }
      }

      if (!detailViewOpen) {
        console.log('Detail view confirmed closed');
        return true;
      }

      await this.delay(200);
    }

    console.log('Detail view may still be open after timeout');
    return false;
  }

  // Removed old extractFromDetailView - using extractCompleteBusinessData instead

  private async extractAdditionalBusinessInfo(): Promise<any> {
    try {
      console.log('Extracting additional business information...');

      const additionalInfo: {
        email: string | undefined;
        socialLinks: string[];
        bookingLink: string | undefined;
        menuLink: string | undefined;
        shareLink: string | undefined;
        photos: string[];
        reviews: any[];
      } = {
        email: undefined,
        socialLinks: [],
        bookingLink: undefined,
        menuLink: undefined,
        shareLink: undefined,
        photos: [],
        reviews: []
      };

      // Scroll down to load more content
      await this.scrollToLoadContent();

      // Extract email
      additionalInfo.email = this.extractBusinessEmail();

      // Extract social links
      additionalInfo.socialLinks = this.extractSocialLinks();

      // Extract booking link
      additionalInfo.bookingLink = this.extractBookingLink();

      // Extract menu link
      additionalInfo.menuLink = this.extractMenuLink();

      // Extract share link
      additionalInfo.shareLink = this.extractShareLink();

      // Extract photos (if needed)
      additionalInfo.photos = this.extractBusinessPhotos();

      // Extract reviews (if needed)
      additionalInfo.reviews = this.extractBusinessReviews();

      return additionalInfo;
    } catch (error) {
      console.error('Failed to extract additional business info:', error);
      return {
        email: undefined,
        socialLinks: [],
        bookingLink: undefined,
        menuLink: undefined,
        shareLink: undefined,
        photos: [],
        reviews: []
      };
    }
  }

  private async scrollToLoadContent(): Promise<void> {
    try {
      console.log('Scrolling to load additional content...');

      // Find the scrollable container (usually the detail panel)
      const scrollContainer = document.querySelector('[role="main"]') ||
                             document.querySelector('.m6QErb') ||
                             document.querySelector('.siAUzd') ||
                             document.body;

      if (scrollContainer) {
        // Scroll down in steps to trigger lazy loading
        const scrollHeight = scrollContainer.scrollHeight;
        const steps = 3;
        const stepSize = scrollHeight / steps;

        for (let i = 1; i <= steps; i++) {
          scrollContainer.scrollTo(0, stepSize * i);
          await this.delay(1000); // Wait for content to load
        }

        // Scroll back to top
        scrollContainer.scrollTo(0, 0);
        await this.delay(500);
      }
    } catch (error) {
      console.error('Error scrolling to load content:', error);
    }
  }

  private extractPlaceIdFromCurrentUrl(): string | undefined {
    try {
      const url = window.location.href;
      return this.extractPlaceIdFromUrl(url);
    } catch (error) {
      console.error('Failed to extract place ID from current URL:', error);
      return undefined;
    }
  }

  private extractPlaceIdFromUrl(url: string): string | undefined {
    try {
      // Extract place ID from Google Maps URLs
      // Format: /place/Business+Name/data=!4m7!3m6!1s0x...!8m2!3d...!4d...!16s%2Fg%2F...
      const placeIdMatch = url.match(/!1s([^!]+)/);
      if (placeIdMatch) {
        return placeIdMatch[1];
      }

      // Alternative format: /place/Business+Name/@lat,lng,zoom/data=...
      const altMatch = url.match(/\/place\/[^\/]+\/@[^\/]+\/data=([^&]+)/);
      if (altMatch) {
        return altMatch[1];
      }

      // Extract from data parameter
      const dataMatch = url.match(/data=([^&]+)/);
      if (dataMatch) {
        return dataMatch[1];
      }

      return undefined;
    } catch (error) {
      console.error('Failed to extract place ID from URL:', error);
      return undefined;
    }
  }

  private extractBusinessEmail(): string | undefined {
    // Email selectors for Google Maps detail view
    const selectors = [
      'a[href^="mailto:"]',
      '[data-value*="email"]',
      '.rogA2c[data-value*="email"]',
    ];

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const href = (element as HTMLElement).getAttribute('href');
        if (href && href.startsWith('mailto:')) {
          const email = href.replace('mailto:', '').trim();
          if (this.looksLikeEmail(email)) {
            console.log(`Found email: ${email}`);
            return email;
          }
        }

        const text = element.textContent?.trim();
        if (text && this.looksLikeEmail(text)) {
          console.log(`Found email from text: ${text}`);
          return text;
        }
      }
    }

    return undefined;
  }

  private extractSocialLinks(): string[] {
    const socialLinks: string[] = [];

    // Social media patterns
    const socialPatterns = [
      /facebook\.com/i,
      /instagram\.com/i,
      /twitter\.com/i,
      /linkedin\.com/i,
      /youtube\.com/i,
      /tiktok\.com/i,
      /yelp\.com/i
    ];

    // Find all external links
    const links = document.querySelectorAll('a[href^="http"]');
    for (let i = 0; i < links.length; i++) {
      const link = links[i] as HTMLAnchorElement;
      const href = link.href;

      if (socialPatterns.some(pattern => pattern.test(href))) {
        if (!socialLinks.includes(href)) {
          socialLinks.push(href);
          console.log(`Found social link: ${href}`);
        }
      }
    }

    return socialLinks;
  }

  private extractBookingLink(): string | undefined {
    // Booking link selectors
    const selectors = [
      'a[href*="booking"]',
      'a[href*="reservation"]',
      'a[href*="appointment"]',
      'a[href*="book"]',
      '[data-value*="booking"]',
      '[aria-label*="book"]',
      '[aria-label*="reservation"]'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLAnchorElement;
      if (element?.href) {
        console.log(`Found booking link: ${element.href}`);
        return element.href;
      }
    }

    return undefined;
  }

  private extractMenuLink(): string | undefined {
    // Menu link selectors
    const selectors = [
      'a[href*="menu"]',
      'a[href*="food"]',
      '[data-value*="menu"]',
      '[aria-label*="menu"]',
      'a[href*="doordash"]',
      'a[href*="ubereats"]',
      'a[href*="grubhub"]'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLAnchorElement;
      if (element?.href) {
        console.log(`Found menu link: ${element.href}`);
        return element.href;
      }
    }

    return undefined;
  }

  private extractShareLink(): string | undefined {
    try {
      // Share link is usually the current page URL
      const currentUrl = window.location.href;
      if (currentUrl.includes('/place/')) {
        console.log(`Found share link: ${currentUrl}`);
        return currentUrl;
      }

      // Alternative: look for share button and extract URL
      const shareButton = document.querySelector('[data-value="share"]') as HTMLElement;
      if (shareButton) {
        // The share URL is typically the current URL
        return currentUrl;
      }

      return undefined;
    } catch (error) {
      console.error('Failed to extract share link:', error);
      return undefined;
    }
  }

  private extractBusinessPhotos(): string[] {
    const photos: string[] = [];

    try {
      // Photo selectors in Google Maps
      const photoSelectors = [
        'img[src*="googleusercontent"]',
        'img[src*="maps.gstatic.com"]',
        '.section-photo img',
        '[data-photo-index] img'
      ];

      for (const selector of photoSelectors) {
        const images = document.querySelectorAll(selector);
        for (let i = 0; i < Math.min(images.length, 10); i++) { // Limit to 10 photos
          const img = images[i] as HTMLImageElement;
          if (img.src && !photos.includes(img.src)) {
            photos.push(img.src);
          }
        }
      }

      console.log(`Found ${photos.length} photos`);
    } catch (error) {
      console.error('Failed to extract photos:', error);
    }

    return photos;
  }

  private extractBusinessReviews(): any[] {
    const reviews: any[] = [];

    try {
      // Review selectors in Google Maps
      const reviewElements = document.querySelectorAll('.jftiEf, .MyEned, .wiI7pd');

      for (let i = 0; i < Math.min(reviewElements.length, 5); i++) { // Limit to 5 reviews
        const reviewEl = reviewElements[i];

        const author = reviewEl.querySelector('.d4r55')?.textContent?.trim();
        const rating = reviewEl.querySelector('.kvMYJc')?.getAttribute('aria-label');
        const text = reviewEl.querySelector('.wiI7pd')?.textContent?.trim();
        const date = reviewEl.querySelector('.rsqaWe')?.textContent?.trim();

        if (author && text) {
          reviews.push({
            author,
            rating: rating ? this.parseRatingFromText(rating) : undefined,
            text,
            date
          });
        }
      }

      console.log(`Found ${reviews.length} reviews`);
    } catch (error) {
      console.error('Failed to extract reviews:', error);
    }

    return reviews;
  }

  private parseRatingFromText(text: string): number | undefined {
    const match = text.match(/(\d+)/);
    return match ? parseInt(match[1]) : undefined;
  }

  private async waitForContentToLoad(): Promise<void> {
    console.log('Waiting for content to fully load...');

    // Wait for basic content indicators
    const contentIndicators = [
      '.DUwDvf', // Business name
      '.Io6YTe', // Address
      '.rogA2c', // Contact info
    ];

    const maxWaitTime = 5000;
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      let contentLoaded = false;

      for (const selector of contentIndicators) {
        const element = document.querySelector(selector);
        if (element && element.textContent?.trim()) {
          contentLoaded = true;
          break;
        }
      }

      if (contentLoaded) {
        console.log('Content indicators found, waiting for stabilization...');
        await this.delay(1000); // Additional wait for content to stabilize
        return;
      }

      await this.delay(200);
    }

    console.log('Content load timeout, proceeding with extraction...');
  }

  private async extractWithRetry<T>(
    extractFn: () => T,
    dataType: string,
    maxRetries: number = 3,
    retryDelay: number = 500
  ): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = extractFn();

        // Check if result is meaningful
        if (result !== undefined && result !== null && result !== '') {
          console.log(`Successfully extracted ${dataType} on attempt ${attempt}`);
          return result;
        }

        if (attempt < maxRetries) {
          console.log(`${dataType} not found on attempt ${attempt}, retrying...`);
          await this.delay(retryDelay);
        }
      } catch (error) {
        console.error(`Error extracting ${dataType} on attempt ${attempt}:`, error);
        if (attempt < maxRetries) {
          await this.delay(retryDelay);
        }
      }
    }

    console.log(`Failed to extract ${dataType} after ${maxRetries} attempts`);
    return extractFn(); // Return final attempt result even if empty
  }
}

// Initialize the extractor in a safe way
(function() {
  'use strict';

  // Check if already initialized to avoid conflicts
  if ((window as any).GoogleMapsExtractorInitialized) {
    console.log('Google Maps Extractor already initialized');
    return;
  }

  (window as any).GoogleMapsExtractorInitialized = true;
  new GoogleMapsExtractor();
})();
