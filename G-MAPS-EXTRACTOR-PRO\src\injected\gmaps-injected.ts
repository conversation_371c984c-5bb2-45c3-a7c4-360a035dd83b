// Google Maps Injected Script - Runs in page context for internal access
// This script intercepts network requests and accesses internal Google Maps state

(function() {
  'use strict';

  interface APIResponse {
    url: string;
    data: any;
    timestamp: number;
    method: string;
  }

  interface InternalBusinessData {
    name?: string;
    address?: string;
    phone?: string;
    website?: string;
    rating?: number;
    category?: string;
    placeId?: string;
    coordinates?: { lat: number; lng: number };
  }

  class GoogleMapsInterceptor {
    private apiResponses: APIResponse[] = [];
    private maxStoredResponses = 50;
    private debug = true;

    constructor() {
      this.initializeInterception();
      this.setupMessageHandlers();
      this.log('🚀 Google Maps Interceptor initialized');
    }

    /**
     * Initialize network interception
     */
    private initializeInterception(): void {
      this.interceptFetch();
      this.interceptXHR();
      this.interceptWebSocket();
    }

    /**
     * Intercept Fetch API
     */
    private interceptFetch(): void {
      const originalFetch = window.fetch;
      
      window.fetch = async (...args: Parameters<typeof fetch>): Promise<Response> => {
        const url = args[0] as string;
        const options = args[1] || {};
        
        try {
          const response = await originalFetch.apply(window, args);
          
          // Check if this is a Google Maps API call
          if (this.isGoogleMapsAPI(url)) {
            this.log(`📡 Intercepted Fetch: ${url}`);
            
            // Clone response to read data without consuming original
            const clonedResponse = response.clone();
            
            try {
              const data = await clonedResponse.json();
              this.storeAPIResponse({
                url,
                data,
                timestamp: Date.now(),
                method: options.method || 'GET'
              });
            } catch (error) {
              // Not JSON, try text
              try {
                const text = await clonedResponse.text();
                if (text.length > 0) {
                  this.storeAPIResponse({
                    url,
                    data: text,
                    timestamp: Date.now(),
                    method: options.method || 'GET'
                  });
                }
              } catch (textError) {
                this.log(`Error reading response: ${textError.message}`);
              }
            }
          }
          
          return response;
        } catch (error) {
          this.log(`Fetch error: ${error.message}`);
          throw error;
        }
      };
    }

    /**
     * Intercept XMLHttpRequest
     */
    private interceptXHR(): void {
      const originalOpen = XMLHttpRequest.prototype.open;
      const originalSend = XMLHttpRequest.prototype.send;
      
      XMLHttpRequest.prototype.open = function(method: string, url: string, ...args: any[]) {
        (this as any)._interceptedUrl = url;
        (this as any)._interceptedMethod = method;
        return originalOpen.apply(this, [method, url, ...args]);
      };
      
      XMLHttpRequest.prototype.send = function(data?: any) {
        const url = (this as any)._interceptedUrl;
        const method = (this as any)._interceptedMethod;
        
        if (url && this.isGoogleMapsAPI(url)) {
          this.addEventListener('load', () => {
            if (this.status >= 200 && this.status < 300) {
              try {
                const responseData = JSON.parse(this.responseText);
                this.storeAPIResponse({
                  url,
                  data: responseData,
                  timestamp: Date.now(),
                  method
                });
                this.log(`📡 Intercepted XHR: ${url}`);
              } catch (error) {
                // Not JSON, store as text if significant
                if (this.responseText.length > 100) {
                  this.storeAPIResponse({
                    url,
                    data: this.responseText,
                    timestamp: Date.now(),
                    method
                  });
                }
              }
            }
          });
        }
        
        return originalSend.apply(this, [data]);
      }.bind(this);
    }

    /**
     * Intercept WebSocket (for real-time updates)
     */
    private interceptWebSocket(): void {
      const originalWebSocket = window.WebSocket;
      
      window.WebSocket = class extends WebSocket {
        constructor(url: string | URL, protocols?: string | string[]) {
          super(url, protocols);
          
          if (this.isGoogleMapsWebSocket(url.toString())) {
            this.addEventListener('message', (event) => {
              try {
                const data = JSON.parse(event.data);
                this.storeAPIResponse({
                  url: url.toString(),
                  data,
                  timestamp: Date.now(),
                  method: 'WEBSOCKET'
                });
                this.log(`📡 Intercepted WebSocket: ${url}`);
              } catch (error) {
                // Not JSON
              }
            });
          }
        }
      } as any;
    }

    /**
     * Check if URL is a Google Maps API endpoint
     */
    private isGoogleMapsAPI(url: string): boolean {
      const googleMapsPatterns = [
        '/maps/api/',
        '/search',
        '/place/',
        'maps.googleapis.com',
        'maps.google.com/maps/api',
        'clients1.google.com/complete/search',
        'www.google.com/search',
        'www.google.com/maps/preview/place',
        'www.google.com/maps/rpc/',
        'maps.google.com/maps/rpc/',
        'maps.google.com/maps/vt',
        'khms0.googleapis.com',
        'khms1.googleapis.com'
      ];
      
      return googleMapsPatterns.some(pattern => url.includes(pattern));
    }

    /**
     * Check if WebSocket URL is Google Maps related
     */
    private isGoogleMapsWebSocket(url: string): boolean {
      return url.includes('maps.google') || url.includes('googleapis.com');
    }

    /**
     * Store API response
     */
    private storeAPIResponse(response: APIResponse): void {
      this.apiResponses.push(response);
      
      // Keep only recent responses to avoid memory issues
      if (this.apiResponses.length > this.maxStoredResponses) {
        this.apiResponses = this.apiResponses.slice(-this.maxStoredResponses);
      }
    }

    /**
     * Setup message handlers for communication with content script
     */
    private setupMessageHandlers(): void {
      window.addEventListener('message', (event) => {
        if (event.source !== window) return;
        
        switch (event.data.type) {
          case 'GMAPS_GET_API_RESPONSES':
            this.sendAPIResponses();
            break;
            
          case 'GMAPS_INTERNAL_REQUEST':
            this.extractInternalData();
            break;
            
          case 'GMAPS_CLEAR_CACHE':
            this.clearCache();
            break;
        }
      });
    }

    /**
     * Send stored API responses to content script
     */
    private sendAPIResponses(): void {
      window.postMessage({
        type: 'GMAPS_API_RESPONSES',
        responses: this.apiResponses
      }, '*');
      
      this.log(`📤 Sent ${this.apiResponses.length} API responses`);
    }

    /**
     * Extract data from Google Maps internal state
     */
    private extractInternalData(): void {
      const businesses: InternalBusinessData[] = [];
      
      try {
        // Try to access various Google Maps internal objects
        const internalSources = [
          () => this.extractFromAppState(),
          () => this.extractFromPageData(),
          () => this.extractFromGoogleObjects(),
          () => this.extractFromReactState(),
          () => this.extractFromAngularScope()
        ];
        
        for (const extractor of internalSources) {
          try {
            const extracted = extractor();
            if (extracted && extracted.length > 0) {
              businesses.push(...extracted);
              this.log(`✅ Extracted ${extracted.length} businesses from internal state`);
            }
          } catch (error) {
            this.log(`Internal extraction method failed: ${error.message}`);
          }
        }
        
      } catch (error) {
        this.log(`Error extracting internal data: ${error.message}`);
      }
      
      // Send results back to content script
      window.postMessage({
        type: 'GMAPS_INTERNAL_RESPONSE',
        businesses: this.deduplicateBusinesses(businesses)
      }, '*');
    }

    /**
     * Extract from APP_INITIALIZATION_STATE
     */
    private extractFromAppState(): InternalBusinessData[] {
      const businesses: InternalBusinessData[] = [];
      
      try {
        const appState = (window as any).APP_INITIALIZATION_STATE;
        if (appState && Array.isArray(appState)) {
          // Google Maps stores data in nested arrays
          this.searchNestedArrays(appState, businesses);
        }
      } catch (error) {
        this.log(`Error extracting from app state: ${error.message}`);
      }
      
      return businesses;
    }

    /**
     * Extract from _pageData
     */
    private extractFromPageData(): InternalBusinessData[] {
      const businesses: InternalBusinessData[] = [];
      
      try {
        const pageData = (window as any)._pageData;
        if (pageData) {
          this.searchObjectForBusinessData(pageData, businesses);
        }
      } catch (error) {
        this.log(`Error extracting from page data: ${error.message}`);
      }
      
      return businesses;
    }

    /**
     * Extract from Google-specific objects
     */
    private extractFromGoogleObjects(): InternalBusinessData[] {
      const businesses: InternalBusinessData[] = [];
      
      try {
        const googleObjects = [
          (window as any).google,
          (window as any).gm_authuser_data,
          (window as any).GM_STATE,
          (window as any).GOOGLE_MAPS_DATA
        ];
        
        for (const obj of googleObjects) {
          if (obj) {
            this.searchObjectForBusinessData(obj, businesses);
          }
        }
      } catch (error) {
        this.log(`Error extracting from Google objects: ${error.message}`);
      }
      
      return businesses;
    }

    /**
     * Extract from React component state (if Google Maps uses React)
     */
    private extractFromReactState(): InternalBusinessData[] {
      const businesses: InternalBusinessData[] = [];
      
      try {
        // Look for React fiber nodes
        const reactElements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
        
        for (const element of reactElements) {
          const reactInstance = (element as any)._reactInternalInstance || 
                               (element as any).__reactInternalInstance;
          
          if (reactInstance) {
            this.searchReactState(reactInstance, businesses);
          }
        }
      } catch (error) {
        this.log(`Error extracting from React state: ${error.message}`);
      }
      
      return businesses;
    }

    /**
     * Extract from Angular scope (if Google Maps uses Angular)
     */
    private extractFromAngularScope(): InternalBusinessData[] {
      const businesses: InternalBusinessData[] = [];
      
      try {
        const angular = (window as any).angular;
        if (angular) {
          const elements = document.querySelectorAll('[ng-app], [data-ng-app], .ng-scope');
          
          for (const element of elements) {
            const scope = angular.element(element).scope();
            if (scope) {
              this.searchObjectForBusinessData(scope, businesses);
            }
          }
        }
      } catch (error) {
        this.log(`Error extracting from Angular scope: ${error.message}`);
      }
      
      return businesses;
    }

    /**
     * Search nested arrays for business-like data
     */
    private searchNestedArrays(arr: any[], businesses: InternalBusinessData[], depth = 0): void {
      if (depth > 10 || !Array.isArray(arr)) return;
      
      for (const item of arr) {
        if (Array.isArray(item)) {
          this.searchNestedArrays(item, businesses, depth + 1);
        } else if (typeof item === 'object' && item !== null) {
          if (this.isBusinessLikeObject(item)) {
            const business = this.convertToBusinessData(item);
            if (business) businesses.push(business);
          } else {
            this.searchObjectForBusinessData(item, businesses, depth + 1);
          }
        }
      }
    }

    /**
     * Search object for business data
     */
    private searchObjectForBusinessData(obj: any, businesses: InternalBusinessData[], depth = 0): void {
      if (depth > 10 || typeof obj !== 'object' || obj === null) return;
      
      // Check if this object itself is business data
      if (this.isBusinessLikeObject(obj)) {
        const business = this.convertToBusinessData(obj);
        if (business) businesses.push(business);
        return;
      }
      
      // Search properties
      for (const key in obj) {
        try {
          const value = obj[key];
          
          if (Array.isArray(value)) {
            this.searchNestedArrays(value, businesses, depth + 1);
          } else if (typeof value === 'object' && value !== null) {
            this.searchObjectForBusinessData(value, businesses, depth + 1);
          }
        } catch (error) {
          // Skip problematic properties
        }
      }
    }

    /**
     * Search React component state
     */
    private searchReactState(reactInstance: any, businesses: InternalBusinessData[], depth = 0): void {
      if (depth > 5 || !reactInstance) return;
      
      try {
        // Check state
        if (reactInstance.state) {
          this.searchObjectForBusinessData(reactInstance.state, businesses, depth + 1);
        }
        
        // Check props
        if (reactInstance.props) {
          this.searchObjectForBusinessData(reactInstance.props, businesses, depth + 1);
        }
        
        // Check children
        if (reactInstance.child) {
          this.searchReactState(reactInstance.child, businesses, depth + 1);
        }
        
        // Check sibling
        if (reactInstance.sibling) {
          this.searchReactState(reactInstance.sibling, businesses, depth + 1);
        }
      } catch (error) {
        // Skip problematic React instances
      }
    }

    /**
     * Check if object looks like business data
     */
    private isBusinessLikeObject(obj: any): boolean {
      if (!obj || typeof obj !== 'object') return false;
      
      const hasName = obj.name || obj.title || obj.businessName;
      const hasLocation = obj.address || obj.location || obj.vicinity || obj.formatted_address;
      const hasBusinessInfo = obj.phone || obj.rating || obj.website || obj.place_id;
      
      return !!(hasName && (hasLocation || hasBusinessInfo));
    }

    /**
     * Convert object to standardized business data
     */
    private convertToBusinessData(obj: any): InternalBusinessData | null {
      try {
        const business: InternalBusinessData = {};
        
        // Name
        business.name = obj.name || obj.title || obj.businessName || '';
        if (!business.name) return null;
        
        // Address
        business.address = obj.address || obj.formatted_address || obj.vicinity || obj.location || '';
        
        // Phone
        business.phone = obj.phone || obj.formatted_phone_number || obj.international_phone_number || '';
        
        // Website
        business.website = obj.website || obj.url || '';
        
        // Rating
        business.rating = obj.rating || obj.averageRating;
        
        // Category
        business.category = obj.category || obj.type || (obj.types && obj.types[0]) || '';
        
        // Place ID
        business.placeId = obj.place_id || obj.placeId || obj.id;
        
        // Coordinates
        if (obj.geometry?.location) {
          business.coordinates = {
            lat: obj.geometry.location.lat,
            lng: obj.geometry.location.lng
          };
        } else if (obj.lat && obj.lng) {
          business.coordinates = {
            lat: obj.lat,
            lng: obj.lng
          };
        }
        
        return business;
      } catch (error) {
        this.log(`Error converting to business data: ${error.message}`);
        return null;
      }
    }

    /**
     * Remove duplicate businesses
     */
    private deduplicateBusinesses(businesses: InternalBusinessData[]): InternalBusinessData[] {
      const seen = new Set<string>();
      return businesses.filter(business => {
        const key = `${business.name}-${business.address}`.toLowerCase();
        if (seen.has(key)) return false;
        seen.add(key);
        return true;
      });
    }

    /**
     * Clear stored cache
     */
    private clearCache(): void {
      this.apiResponses = [];
      this.log('🗑️ Cache cleared');
    }

    private log(message: string): void {
      if (this.debug) {
        console.log(`[GMapsInterceptor] ${message}`);
      }
    }
  }

  // Initialize interceptor when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new GoogleMapsInterceptor();
    });
  } else {
    new GoogleMapsInterceptor();
  }

})();
