import React, { useState, useEffect } from 'react';
import ReactD<PERSON> from 'react-dom/client';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  TextField,
  Grid,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  ThemeProvider,
  CssBaseline,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Save as SaveIcon,
  Restore as RestoreIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Palette as PaletteIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { lightTheme } from '../theme';

interface ExtensionSettings {
  autoSave: boolean;
  notifications: boolean;
  defaultExportFormat: 'csv' | 'excel' | 'json';
  theme: 'light' | 'dark' | 'auto';
  maxResults: number;
  extractionDelay: number;
  enableAdvancedFilters: boolean;
  autoBackup: boolean;
}

const defaultSettings: ExtensionSettings = {
  autoSave: true,
  notifications: true,
  defaultExportFormat: 'csv',
  theme: 'light',
  maxResults: 50,
  extractionDelay: 1000,
  enableAdvancedFilters: false,
  autoBackup: false,
};

const Options: React.FC = () => {
  const [settings, setSettings] = useState<ExtensionSettings>(defaultSettings);
  const [saving, setSaving] = useState(false);
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.sync.get('extensionSettings');
        if (result.extensionSettings) {
          setSettings({ ...defaultSettings, ...result.extensionSettings });
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.sync.set({ extensionSettings: settings });
        setSaveMessage('Settings saved successfully!');
        setTimeout(() => setSaveMessage(null), 3000);
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      setSaveMessage('Failed to save settings. Please try again.');
      setTimeout(() => setSaveMessage(null), 3000);
    } finally {
      setSaving(false);
    }
  };

  const resetSettings = async () => {
    setSettings(defaultSettings);
    setShowResetDialog(false);
    await saveSettings();
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'gmaps-extractor-settings.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string);
          setSettings({ ...defaultSettings, ...importedSettings });
        } catch (error) {
          console.error('Failed to import settings:', error);
          setSaveMessage('Failed to import settings. Invalid file format.');
          setTimeout(() => setSaveMessage(null), 3000);
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <ThemeProvider theme={lightTheme}>
      <CssBaseline />
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            <SettingsIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
            G Maps Extractor Pro - Settings
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure your extension preferences and behavior
          </Typography>
        </Box>

        {/* Save Message */}
        {saveMessage && (
          <Alert 
            severity={saveMessage.includes('Failed') ? 'error' : 'success'} 
            sx={{ mb: 3 }}
          >
            {saveMessage}
          </Alert>
        )}

        {/* General Settings */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <NotificationsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              General Settings
            </Typography>
            
            <Grid container spacing={3}>
              <Grid size={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.autoSave}
                      onChange={(e) => setSettings({ ...settings, autoSave: e.target.checked })}
                    />
                  }
                  label="Auto-save extracted data"
                />
              </Grid>
              
              <Grid size={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.notifications}
                      onChange={(e) => setSettings({ ...settings, notifications: e.target.checked })}
                    />
                  }
                  label="Show notifications"
                />
              </Grid>

              <Grid size={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.enableAdvancedFilters}
                      onChange={(e) => setSettings({ ...settings, enableAdvancedFilters: e.target.checked })}
                    />
                  }
                  label="Enable advanced filters"
                />
              </Grid>

              <Grid size={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.autoBackup}
                      onChange={(e) => setSettings({ ...settings, autoBackup: e.target.checked })}
                    />
                  }
                  label="Auto backup data"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Extraction Settings */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <DownloadIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Extraction Settings
            </Typography>
            
            <Grid container spacing={3}>
              <Grid size={6}>
                <TextField
                  fullWidth
                  label="Max Results per Search"
                  type="number"
                  value={settings.maxResults}
                  onChange={(e) => setSettings({ ...settings, maxResults: parseInt(e.target.value) || 50 })}
                  inputProps={{ min: 1, max: 200 }}
                  helperText="Maximum number of businesses to extract per search"
                />
              </Grid>

              <Grid size={6}>
                <TextField
                  fullWidth
                  label="Extraction Delay (ms)"
                  type="number"
                  value={settings.extractionDelay}
                  onChange={(e) => setSettings({ ...settings, extractionDelay: parseInt(e.target.value) || 1000 })}
                  inputProps={{ min: 500, max: 5000 }}
                  helperText="Delay between extractions to avoid rate limiting"
                />
              </Grid>

              <Grid size={6}>
                <FormControl fullWidth>
                  <InputLabel>Default Export Format</InputLabel>
                  <Select
                    value={settings.defaultExportFormat}
                    onChange={(e) => setSettings({ ...settings, defaultExportFormat: e.target.value as any })}
                    label="Default Export Format"
                  >
                    <MenuItem value="csv">CSV</MenuItem>
                    <MenuItem value="excel">Excel</MenuItem>
                    <MenuItem value="json">JSON</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid size={6}>
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={settings.theme}
                    onChange={(e) => setSettings({ ...settings, theme: e.target.value as any })}
                    label="Theme"
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                    <MenuItem value="auto">Auto</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Data Management
            </Typography>
            
            <Grid container spacing={2}>
              <Grid size={4}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={exportSettings}
                >
                  Export Settings
                </Button>
              </Grid>

              <Grid size={4}>
                <Button
                  fullWidth
                  variant="outlined"
                  component="label"
                  startIcon={<UploadIcon />}
                >
                  Import Settings
                  <input
                    type="file"
                    hidden
                    accept=".json"
                    onChange={importSettings}
                  />
                </Button>
              </Grid>

              <Grid size={4}>
                <Button
                  fullWidth
                  variant="outlined"
                  color="error"
                  startIcon={<RestoreIcon />}
                  onClick={() => setShowResetDialog(true)}
                >
                  Reset to Default
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={saveSettings}
            disabled={saving}
            size="large"
          >
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>

        {/* Reset Confirmation Dialog */}
        <Dialog open={showResetDialog} onClose={() => setShowResetDialog(false)}>
          <DialogTitle>Reset Settings</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to reset all settings to their default values? 
              This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowResetDialog(false)}>Cancel</Button>
            <Button onClick={resetSettings} color="error" variant="contained">
              Reset
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </ThemeProvider>
  );
};

// Mount the component
const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(<Options />);

export default Options;
