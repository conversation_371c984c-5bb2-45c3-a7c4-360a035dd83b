{"name": "G Maps Extractor Pro", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "build:hybrid": "node build-hybrid.js", "preview": "vite preview", "server": "node server.js", "server:dev": "nodemon server.js", "start": "concurrently \"npm run dev\" \"npm run server:dev\"", "build:all": "npm run build && npm run server", "test:build": "node test-build.js", "test:hybrid": "node build-hybrid.js && node test-extension.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/styled-engine-sc": "^7.0.2", "@mui/x-charts": "^8.1.0", "axios": "^1.8.4", "cors": "^2.8.5", "express": "^5.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^2.15.3", "styled-components": "^6.1.17"}, "devDependencies": {"@types/chrome": "^0.0.316", "@types/node": "^22.14.1", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.2.0", "json-server": "^1.0.0-beta.3", "nodemon": "^3.1.10", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-plugin-html": "^3.2.2"}}