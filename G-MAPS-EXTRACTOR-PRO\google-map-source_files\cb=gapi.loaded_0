gapi.loaded_0(function(_){var window=this;
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([]);
var da,ha,la,pa,ta,va,Da,Ea;da=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};ha=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
la=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};_.na=la(this);pa=function(a,b){if(b)a:{var c=_.na;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ha(c,a,{configurable:!0,writable:!0,value:b})}};
pa("Symbol",function(a){if(a)return a;var b=function(f,h){this.l2=f;ha(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.l2};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
pa("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=_.na[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ha(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ta(da(this))}})}return a});ta=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.ua=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b};if(typeof Object.setPrototypeOf=="function")va=Object.setPrototypeOf;else{var wa;a:{var xa={a:!0},ya={};try{ya.__proto__=xa;wa=ya.a;break a}catch(a){}wa=!1}va=wa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.za=va;
_.Aa=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:da(a)};throw Error("b`"+String(a));};Da=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};Ea=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Da(d,e)&&(a[e]=d[e])}return a};pa("Object.assign",function(a){return a||Ea});
pa("globalThis",function(a){return a||_.na});pa("Reflect.setPrototypeOf",function(a){return a?a:_.za?function(b,c){try{return(0,_.za)(b,c),!0}catch(d){return!1}}:null});
pa("Promise",function(a){function b(){this.Af=null}function c(h){return h instanceof e?h:new e(function(k){k(h)})}if(a)return a;b.prototype.zP=function(h){if(this.Af==null){this.Af=[];var k=this;this.AP(function(){k.V8()})}this.Af.push(h)};var d=_.na.setTimeout;b.prototype.AP=function(h){d(h,0)};b.prototype.V8=function(){for(;this.Af&&this.Af.length;){var h=this.Af;this.Af=[];for(var k=0;k<h.length;++k){var l=h[k];h[k]=null;try{l()}catch(m){this.Yp(m)}}}this.Af=null};b.prototype.Yp=function(h){this.AP(function(){throw h;
})};var e=function(h){this.Ca=0;this.qf=void 0;this.Fr=[];this.aW=!1;var k=this.BF();try{h(k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.BF=function(){function h(m){return function(n){l||(l=!0,m.call(k,n))}}var k=this,l=!1;return{resolve:h(this.dfa),reject:h(this.qK)}};e.prototype.dfa=function(h){if(h===this)this.qK(new TypeError("A Promise cannot resolve to itself"));else if(h instanceof e)this.Iga(h);else{a:switch(typeof h){case "object":var k=h!=null;break a;case "function":k=!0;break a;
default:k=!1}k?this.cfa(h):this.XS(h)}};e.prototype.cfa=function(h){var k=void 0;try{k=h.then}catch(l){this.qK(l);return}typeof k=="function"?this.Jga(k,h):this.XS(h)};e.prototype.qK=function(h){this.Y_(2,h)};e.prototype.XS=function(h){this.Y_(1,h)};e.prototype.Y_=function(h,k){if(this.Ca!=0)throw Error("c`"+h+"`"+k+"`"+this.Ca);this.Ca=h;this.qf=k;this.Ca===2&&this.sfa();this.W8()};e.prototype.sfa=function(){var h=this;d(function(){if(h.pda()){var k=_.na.console;typeof k!=="undefined"&&k.error(h.qf)}},
1)};e.prototype.pda=function(){if(this.aW)return!1;var h=_.na.CustomEvent,k=_.na.Event,l=_.na.dispatchEvent;if(typeof l==="undefined")return!0;typeof h==="function"?h=new h("unhandledrejection",{cancelable:!0}):typeof k==="function"?h=new k("unhandledrejection",{cancelable:!0}):(h=_.na.document.createEvent("CustomEvent"),h.initCustomEvent("unhandledrejection",!1,!0,h));h.promise=this;h.reason=this.qf;return l(h)};e.prototype.W8=function(){if(this.Fr!=null){for(var h=0;h<this.Fr.length;++h)f.zP(this.Fr[h]);
this.Fr=null}};var f=new b;e.prototype.Iga=function(h){var k=this.BF();h.wy(k.resolve,k.reject)};e.prototype.Jga=function(h,k){var l=this.BF();try{h.call(k,l.resolve,l.reject)}catch(m){l.reject(m)}};e.prototype.then=function(h,k){function l(q,r){return typeof q=="function"?function(w){try{m(q(w))}catch(u){n(u)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.wy(l(h,m),l(k,n));return p};e.prototype.catch=function(h){return this.then(void 0,h)};e.prototype.wy=function(h,k){function l(){switch(m.Ca){case 1:h(m.qf);
break;case 2:k(m.qf);break;default:throw Error("d`"+m.Ca);}}var m=this;this.Fr==null?f.zP(l):this.Fr.push(l);this.aW=!0};e.resolve=c;e.reject=function(h){return new e(function(k,l){l(h)})};e.race=function(h){return new e(function(k,l){for(var m=_.Aa(h),n=m.next();!n.done;n=m.next())c(n.value).wy(k,l)})};e.all=function(h){var k=_.Aa(h),l=k.next();return l.done?c([]):new e(function(m,n){function p(w){return function(u){q[w]=u;r--;r==0&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(l.value).wy(p(q.length-
1),n),l=k.next();while(!l.done)})};return e});var Ia=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
pa("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var h=0;h<f&&c<e;)if(d[c++]!=b[h++])return!1;return h>=f}});pa("Object.setPrototypeOf",function(a){return a||_.za});pa("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});
pa("WeakMap",function(a){function b(){}function c(l){var m=typeof l;return m==="object"&&l!==null||m==="function"}function d(l){if(!Da(l,f)){var m=new b;ha(l,f,{value:m})}}function e(l){var m=Object[l];m&&(Object[l]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),m=Object.seal({}),n=new a([[l,2],[m,3]]);if(n.get(l)!=2||n.get(m)!=3)return!1;n.delete(l);n.set(m,4);return!n.has(l)&&n.get(m)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var h=0,k=function(l){this.Da=(h+=Math.random()+1).toString();if(l){l=_.Aa(l);for(var m;!(m=l.next()).done;)m=m.value,this.set(m[0],m[1])}};k.prototype.set=function(l,m){if(!c(l))throw Error("e");d(l);if(!Da(l,f))throw Error("f`"+l);l[f][this.Da]=m;return this};k.prototype.get=function(l){return c(l)&&Da(l,f)?l[f][this.Da]:void 0};k.prototype.has=function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)};k.prototype.delete=
function(l){return c(l)&&Da(l,f)&&Da(l[f],this.Da)?delete l[f][this.Da]:!1};return k});
pa("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var k=Object.seal({x:4}),l=new a(_.Aa([[k,"s"]]));if(l.get(k)!="s"||l.size!=1||l.get({x:4})||l.set({x:4},"t")!=l||l.size!=2)return!1;var m=l.entries(),n=m.next();if(n.done||n.value[0]!=k||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(k){this[0]={};this[1]=
f();this.size=0;if(k){k=_.Aa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(k,l){k=k===0?0:k;var m=d(this,k);m.list||(m.list=this[0][m.id]=[]);m.Ve?m.Ve.value=l:(m.Ve={next:this[1],Lk:this[1].Lk,head:this[1],key:k,value:l},m.list.push(m.Ve),this[1].Lk.next=m.Ve,this[1].Lk=m.Ve,this.size++);return this};c.prototype.delete=function(k){k=d(this,k);return k.Ve&&k.list?(k.list.splice(k.index,1),k.list.length||delete this[0][k.id],k.Ve.Lk.next=k.Ve.next,k.Ve.next.Lk=
k.Ve.Lk,k.Ve.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Lk=f();this.size=0};c.prototype.has=function(k){return!!d(this,k).Ve};c.prototype.get=function(k){return(k=d(this,k).Ve)&&k.value};c.prototype.entries=function(){return e(this,function(k){return[k.key,k.value]})};c.prototype.keys=function(){return e(this,function(k){return k.key})};c.prototype.values=function(){return e(this,function(k){return k.value})};c.prototype.forEach=function(k,l){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,k.call(l,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(k,l){var m=l&&typeof l;m=="object"||m=="function"?b.has(l)?m=b.get(l):(m=""+ ++h,b.set(l,m)):m="p_"+l;var n=k[0][m];if(n&&Da(k[0],m))for(k=0;k<n.length;k++){var p=n[k];if(l!==l&&p.key!==p.key||l===p.key)return{id:m,list:n,index:k,Ve:p}}return{id:m,list:n,index:-1,Ve:void 0}},e=function(k,l){var m=k[1];return ta(function(){if(m){for(;m.head!=k[1];)m=m.Lk;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:l(m)};m=null}return{done:!0,value:void 0}})},f=function(){var k={};return k.Lk=k.next=k.head=k},h=0;return c});
pa("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(_.Aa([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(h){return!1}}())return a;var b=function(c){this.Sa=new Map;if(c){c=
_.Aa(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.Sa.size};b.prototype.add=function(c){c=c===0?0:c;this.Sa.set(c,c);this.size=this.Sa.size;return this};b.prototype.delete=function(c){c=this.Sa.delete(c);this.size=this.Sa.size;return c};b.prototype.clear=function(){this.Sa.clear();this.size=0};b.prototype.has=function(c){return this.Sa.has(c)};b.prototype.entries=function(){return this.Sa.entries()};b.prototype.values=function(){return this.Sa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.Sa.forEach(function(f){return c.call(d,f,f,e)})};return b});var Ka=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};pa("Array.prototype.entries",function(a){return a?a:function(){return Ka(this,function(b,c){return[b,c]})}});
pa("Array.prototype.keys",function(a){return a?a:function(){return Ka(this,function(b){return b})}});pa("String.prototype.codePointAt",function(a){return a?a:function(b){var c=Ia(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(b>=0&&b<d){b|=0;var e=c.charCodeAt(b);if(e<55296||e>56319||b+1===d)return e;b=c.charCodeAt(b+1);return b<56320||b>57343?e:(e-55296)*1024+b+9216}}});
pa("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(e<0||e>1114111||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);e<=65535?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});pa("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push([d,b[d]]);return c}});
pa("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"endsWith");c===void 0&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;e>0&&c>0;)if(d[--c]!=b[--e])return!1;return e<=0}});pa("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
var Ma=function(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var f=a[e];if(b.call(c,f,e,a))return{aV:e,SD:f}}return{aV:-1,SD:void 0}};pa("Array.prototype.find",function(a){return a?a:function(b,c){return Ma(this,b,c).SD}});pa("Array.prototype.values",function(a){return a?a:function(){return Ka(this,function(b,c){return c})}});
pa("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});pa("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
pa("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});pa("String.prototype.includes",function(a){return a?a:function(b,c){return Ia(this,b,"includes").indexOf(b,c||0)!==-1}});
pa("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(k){return k};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var h=0;!(f=b.next()).done;)e.push(c.call(d,f.value,h++))}else for(f=b.length,h=0;h<f;h++)e.push(c.call(d,b[h],h));return e}});pa("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)Da(b,d)&&c.push(b[d]);return c}});
pa("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});pa("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});pa("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});pa("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
pa("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});pa("Array.prototype.flatMap",function(a){return a?a:function(b,c){var d=[];Array.prototype.forEach.call(this,function(e,f){e=b.call(c,e,f,this);Array.isArray(e)?d.push.apply(d,e):d.push(e)});return d}});pa("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});
pa("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});pa("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});
pa("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});var Na=function(a){a=Math.trunc(a)||0;a<0&&(a+=this.length);if(!(a<0||a>=this.length))return this[a]};
pa("Array.prototype.at",function(a){return a?a:Na});var Pa=function(a){return a?a:Na};pa("Int8Array.prototype.at",Pa);pa("Uint8Array.prototype.at",Pa);pa("Uint8ClampedArray.prototype.at",Pa);pa("Int16Array.prototype.at",Pa);pa("Uint16Array.prototype.at",Pa);pa("Int32Array.prototype.at",Pa);pa("Uint32Array.prototype.at",Pa);pa("Float32Array.prototype.at",Pa);pa("Float64Array.prototype.at",Pa);pa("String.prototype.at",function(a){return a?a:Na});
pa("Array.prototype.findIndex",function(a){return a?a:function(b,c){return Ma(this,b,c).aV}});_.Ta={};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
_.Va=_.Va||{};_.Xa=this||self;_.$a=_.Xa._F_toggles||[];_.ab="closure_uid_"+(Math.random()*1E9>>>0);_.bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.t=function(a,b){a=a.split(".");for(var c=_.Xa,d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b};
_.eb=function(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.mt=function(d,e,f){for(var h=Array(arguments.length-2),k=2;k<arguments.length;k++)h[k-2]=arguments[k];return b.prototype[e].apply(d,h)}};_.gb=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},card:{params:{s:"#",userid:"&"},
url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
blogger:{url:":socialhost:/:session_prefix:_/widget/render/blogger?usegapi=1"},youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},appfinder:{url:"https://workspace.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{rpc:"/rpc",root:"https://content.googleapis.com",
"root-1p":"https://clients6.google.com",useGapiForXd3:!0,xd3:"/static/proxy.html",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4},client:{perApiBatch:!0},gen204logger:{interval:3E4,rate:.001,batch:!1}});
var ob;_.jb=function(a){return function(){return _.hb[a].apply(this,arguments)}};_.lb=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.lb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b);this.rZ=!0};ob=function(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");_.lb.call(this,c+a[d])};_.hb=[];_.eb(_.lb,Error);_.lb.prototype.name="CustomError";_.eb(ob,_.lb);ob.prototype.name="AssertionError";
var xb,yb,zb;_.pb=function(a,b){return _.hb[a]=b};_.rb=function(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};_.tb=function(a,b){return(0,_.sb)(a,b)>=0};_.ub=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.vb=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};
_.y=function(a,b){a.prototype=(0,_.ua)(b.prototype);a.prototype.constructor=a;if(_.za)(0,_.za)(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype};_.wb=function(a,b){a=a.split(".");b=b||_.Xa;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b};xb=function(a){var b=_.wb("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1};
yb=function(a,b,c){return a.call.apply(a.bind,arguments)};zb=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.z=function(a,b,c){_.z=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?yb:zb;return _.z.apply(null,arguments)};_.sb=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Ab=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;c<0&&(c=Math.max(0,a.length+c));if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.lastIndexOf(b,c);for(;c>=0;c--)if(c in a&&a[c]===b)return c;return-1};_.Bb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Gb=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=typeof a==="string"?a.split(""):a,h=0;h<c;h++)if(h in f){var k=f[h];b.call(void 0,k,h,a)&&(d[e++]=k)}return d};_.Ib=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=typeof a==="string"?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Jb=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Nb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=typeof a==="string"?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};var Ob=!!(_.$a[0]&4096),Pb=!!(_.$a[0]&8192),Qb=!!(_.$a[0]&16),Rb=!!(_.$a[0]>>15&1);_.Sb=Ob?Pb:xb(610401301);_.Ub=Ob?Qb:xb(**********);_.Vb=Ob?Rb:xb(651175828);_.Wb=function(a){_.Wb[" "](a);return a};_.Wb[" "]=function(){};
/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ac,cc,pc,Ac,Lc,Zc,id;_.Yb=function(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Zb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};ac=function(){var a=null;if(!$b)return a;try{var b=function(c){return c};a=$b.createPolicy("gapi#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};cc=function(){bc===void 0&&(bc=ac());return bc};_.ec=function(a){var b=cc();a=b?b.createHTML(a):a;return new _.dc(a)};
_.fc=function(a){if(a instanceof _.dc)return a.NY;throw Error("j");};_.hc=function(a){return new _.gc(a)};_.jc=function(a){var b=cc();a=b?b.createScriptURL(a):a;return new _.ic(a)};_.kc=function(a){if(a instanceof _.ic)return a.OY;throw Error("j");};_.mc=function(a){return a instanceof _.lc};_.nc=function(a){if(_.mc(a))return a.QY;throw Error("j");};pc=function(a){return new _.oc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};_.rc=function(a){if(qc.test(a))return a};
_.sc=function(a){return a instanceof _.lc?_.nc(a):_.rc(a)};_.tc=function(a,b){if(a instanceof _.dc)return a;a=String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");if(b==null?0:b.msa)a=a.replace(/(^|[\r\n\t ]) /g,"$1&#160;");if(b==null?0:b.rea)a=a.replace(/(\r\n|\n|\r)/g,"<br>");if(b==null?0:b.nsa)a=a.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>');return _.ec(a)};
_.vc=function(a){var b=_.uc.apply(1,arguments);if(b.length===0)return _.jc(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return _.jc(c)};_.wc=function(a,b){return a.lastIndexOf(b,0)==0};_.xc=function(a){return/^[\s\xa0]*$/.test(a)};_.yc=function(a,b){return a.indexOf(b)!=-1};
_.Dc=function(a,b){var c=0;a=(0,_.zc)(String(a)).split(".");b=(0,_.zc)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(f[0].length==0&&h[0].length==0)break;c=Ac(f[1].length==0?0:parseInt(f[1],10),h[1].length==0?0:parseInt(h[1],10))||Ac(f[2].length==0,h[2].length==0)||Ac(f[2],h[2]);f=f[3];h=h[3]}while(c==0)}return c};
Ac=function(a,b){return a<b?-1:a>b?1:0};_.Ec=function(a,b){b=_.sc(b);b!==void 0&&(a.href=b)};_.Fc=function(a,b,c,d){b=_.sc(b);return b!==void 0?a.open(b,c,d):null};_.Gc=function(a,b){b=b===void 0?document:b;var c,d;b=(d=(c=b).querySelector)==null?void 0:d.call(c,a+"[nonce]");return b==null?"":b.nonce||b.getAttribute("nonce")||""};_.Hc=function(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("j");a.innerHTML=_.fc(b)};
_.Jc=function(){var a=_.Xa.navigator;return a&&(a=a.userAgent)?a:""};Lc=function(a){if(!_.Sb||!_.Kc)return!1;for(var b=0;b<_.Kc.brands.length;b++){var c=_.Kc.brands[b].brand;if(c&&_.yc(c,a))return!0}return!1};_.Mc=function(a){return _.yc(_.Jc(),a)};_.Nc=function(a){for(var b=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g"),c=[],d;d=b.exec(a);)c.push([d[1],d[2],d[3]||void 0]);return c};_.Oc=function(){return _.Sb?!!_.Kc&&_.Kc.brands.length>0:!1};_.Pc=function(){return _.Oc()?!1:_.Mc("Opera")};
_.Qc=function(){return _.Oc()?!1:_.Mc("Trident")||_.Mc("MSIE")};_.Sc=function(){return _.Oc()?!1:_.Mc("Edge")};_.Tc=function(){return _.Oc()?Lc("Microsoft Edge"):_.Mc("Edg/")};_.Uc=function(){return _.Oc()?Lc("Opera"):_.Mc("OPR")};_.Vc=function(){return _.Mc("Firefox")||_.Mc("FxiOS")};_.Wc=function(){return _.Oc()?Lc("Chromium"):(_.Mc("Chrome")||_.Mc("CriOS"))&&!_.Sc()||_.Mc("Silk")};
_.Xc=function(a){var b={};a.forEach(function(c){b[c[0]]=c[1]});return function(c){return b[c.find(function(d){return d in b})]||""}};_.Yc=function(a){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])return b[1];b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];return b};
Zc=function(){return _.Sb?!!_.Kc&&!!_.Kc.platform:!1};_.$c=function(){return Zc()?_.Kc.platform==="Android":_.Mc("Android")};_.ad=function(){return _.Mc("iPhone")&&!_.Mc("iPod")&&!_.Mc("iPad")};_.bd=function(){return _.ad()||_.Mc("iPad")||_.Mc("iPod")};_.cd=function(){return Zc()?_.Kc.platform==="macOS":_.Mc("Macintosh")};_.dd=function(){return Zc()?_.Kc.platform==="Windows":_.Mc("Windows")};_.ed=function(){return Zc()?_.Kc.platform==="Chrome OS":_.Mc("CrOS")};
_.fd=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a};_.gd=function(a){return _.fd(a,a)};_.uc=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};_.jd=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"};_.kd=function(a){var b=_.jd(a);return b=="array"||b=="object"&&typeof a.length=="number"};_.ld=function(){return Date.now()};var md=globalThis.trustedTypes,$b=md,bc;_.dc=function(a){this.NY=a};_.dc.prototype.toString=function(){return this.NY+""};_.nd=function(){return new _.dc(md?md.emptyHTML:"")}();_.gc=function(a){this.PY=a};_.gc.prototype.toString=function(){return this.PY};_.ic=function(a){this.OY=a};_.ic.prototype.toString=function(){return this.OY+""};_.lc=function(a){this.QY=a};_.lc.prototype.toString=function(){return this.QY};_.od=new _.lc("about:invalid#zClosurez");var qc;_.oc=function(a){this.xj=a};_.pd=[pc("data"),pc("http"),pc("https"),pc("mailto"),pc("ftp"),new _.oc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.qd=function(){return typeof URL==="function"}();qc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;_.rd=function(a,b){this.width=a;this.height=b};_.sd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.g=_.rd.prototype;_.g.clone=function(){return new _.rd(this.width,this.height)};_.g.by=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.by()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};_.zc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};_.td=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};_.ud=Math.random()*2147483648|0;var vd;vd=_.Xa.navigator;_.Kc=vd?vd.userAgentData||null:null;var Nd,Od,Wd;_.xd=_.Pc();_.yd=_.Qc();_.zd=_.Mc("Edge");_.Ad=_.zd||_.yd;_.Bd=_.Mc("Gecko")&&!(_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge"))&&!(_.Mc("Trident")||_.Mc("MSIE"))&&!_.Mc("Edge");_.Cd=_.yc(_.Jc().toLowerCase(),"webkit")&&!_.Mc("Edge");_.Dd=_.Cd&&_.Mc("Mobile");_.Ed=_.cd();_.Fd=_.dd();_.Gd=(Zc()?_.Kc.platform==="Linux":_.Mc("Linux"))||_.ed();_.Id=_.$c();_.Jd=_.ad();_.Kd=_.Mc("iPad");_.Ld=_.Mc("iPod");_.Md=_.bd();Nd=function(){var a=_.Xa.document;return a?a.documentMode:void 0};
a:{var Pd="",Td=function(){var a=_.Jc();if(_.Bd)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.zd)return/Edge\/([\d\.]+)/.exec(a);if(_.yd)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.Cd)return/WebKit\/(\S+)/.exec(a);if(_.xd)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Td&&(Pd=Td?Td[1]:"");if(_.yd){var Ud=Nd();if(Ud!=null&&Ud>parseFloat(Pd)){Od=String(Ud);break a}}Od=Pd}_.Vd=Od;if(_.Xa.document&&_.yd){var Xd=Nd();Wd=Xd?Xd:parseInt(_.Vd,10)||void 0}else Wd=void 0;_.Yd=Wd;var de,ke,je;_.ae=function(a){return a?new _.Zd(_.$d(a)):id||(id=new _.Zd)};_.be=function(a,b){return typeof b==="string"?a.getElementById(b):b};_.ce=function(a,b,c,d){a=d||a;return(b=b&&b!="*"?String(b).toUpperCase():"")||c?a.querySelectorAll(b+(c?"."+c:"")):a.getElementsByTagName("*")};
_.ee=function(a,b){_.Zb(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:de.hasOwnProperty(d)?a.setAttribute(de[d],c):_.wc(d,"aria-")||_.wc(d,"data-")?a.setAttribute(d,c):a[d]=c})};de={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.ge=function(a){return _.fe(a||window)};
_.fe=function(a){a=a.document;a=_.he(a)?a.documentElement:a.body;return new _.rd(a.clientWidth,a.clientHeight)};_.ie=function(a){return a?a.defaultView:window};_.le=function(a,b){var c=b[1],d=je(a,String(b[0]));c&&(typeof c==="string"?d.className=c:Array.isArray(c)?d.className=c.join(" "):_.ee(d,c));b.length>2&&ke(a,d,b,2);return d};
ke=function(a,b,c,d){function e(k){k&&b.appendChild(typeof k==="string"?a.createTextNode(k):k)}for(;d<c.length;d++){var f=c[d];if(!_.kd(f)||_.vb(f)&&f.nodeType>0)e(f);else{a:{if(f&&typeof f.length=="number"){if(_.vb(f)){var h=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){h=typeof f.item=="function";break a}}h=!1}_.Bb(h?_.Yb(f):f,e)}}};_.me=function(a){return je(document,a)};
je=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};_.he=function(a){return a.compatMode=="CSS1Compat"};_.ne=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.oe=function(a,b){ke(_.$d(a),a,arguments,1)};_.pe=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.qe=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.re=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.se=function(a){return a.children!=void 0?a.children:Array.prototype.filter.call(a.childNodes,function(b){return b.nodeType==1})};_.te=function(a){return _.vb(a)&&a.nodeType==1};
_.ue=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.$d=function(a){return a.nodeType==9?a:a.ownerDocument||a.document};
_.ve=function(a,b){if("textContent"in a)a.textContent=b;else if(a.nodeType==3)a.data=String(b);else if(a.firstChild&&a.firstChild.nodeType==3){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.pe(a),a.appendChild(_.$d(a).createTextNode(String(b)))};_.Zd=function(a){this.Bc=a||_.Xa.document||document};_.g=_.Zd.prototype;_.g.Ha=_.ae;_.g.uL=_.jb(0);_.g.ub=function(){return this.Bc};_.g.O=_.jb(1);_.g.getElementsByTagName=function(a,b){return(b||this.Bc).getElementsByTagName(String(a))};
_.g.uH=_.jb(2);_.g.wa=function(a,b,c){return _.le(this.Bc,arguments)};_.g.createElement=function(a){return je(this.Bc,a)};_.g.createTextNode=function(a){return this.Bc.createTextNode(String(a))};_.g.getWindow=function(){return this.Bc.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.oe;_.g.canHaveChildren=_.ne;_.g.ne=_.pe;_.g.xV=_.qe;_.g.removeNode=_.re;_.g.EG=_.se;_.g.isElement=_.te;_.g.contains=_.ue;_.g.XG=_.$d;_.g.vj=_.jb(3);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.we=function(a){return a===null?"null":a===void 0?"undefined":a};_.xe=window;_.ye=document;_.ze=_.xe.location;_.Ae=/\[native code\]/;_.Be=function(a,b,c){return a[b]=a[b]||c};_.Ce=function(){var a;if((a=Object.create)&&_.Ae.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.De=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.Ee=function(a,b){a=a||{};for(var c in a)_.De(a,c)&&(b[c]=a[c])};_.Fe=_.Be(_.xe,"gapi",{});_.Ge=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=decodeURIComponent(a[2])}catch(e){}return c};_.He=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);_.Ie=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");
_.Je=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");_.Le=function(a,b,c){_.Ke(a,b,c,"add","at")};_.Ke=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.Me={};_.Me=_.Be(_.xe,"___jsl",_.Ce());_.Be(_.Me,"I",0);_.Be(_.Me,"hel",10);var Ne,Pe,Qe,Re,Ue,Se,Te,Ve,We;Ne=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Pe=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};Qe=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Re=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Qe(a[d])&&!Qe(b[d])?Re(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Qe(b[d])?[]:{},Re(a[d],b[d])):a[d]=b[d])};
Ue=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Ne("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Se())if(e=Te(c),d.push(25),typeof e===
"object")return e;return{}}};Se=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Te=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};Ve=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Re(c,b);a.push(c)};
We=function(a){Pe(!0);var b=window.___gcfg,c=Ne("cu"),d=window.___gu;b&&b!==d&&(Ve(c,b),window.___gu=b);b=Ne("cu");var e=document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,Ne("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&d.push(k);d.length==0&&e.length>0&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),(f=d[e])?(h=f.nodeType,f=h==3||
h==4?f.nodeValue:f.textContent||""):f=void 0,h=d[e].nonce||d[e].getAttribute("nonce"),(f=Ue(f,h))&&b.push(f));a&&Ve(c,a);d=Ne("cd");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);d=Ne("ci");a=0;for(b=d.length;a<b;++a)Re(Pe(),d[a],!0);a=0;for(b=c.length;a<b;++a)Re(Pe(),c[a],!0)};_.Xe=function(a,b){var c=Pe();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&typeof c==="object"&&d<e;++d)c=c[a[d]];return d===a.length&&c!==void 0?c:b};
_.Ye=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;We(c)};var Ze=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.Be(_.Me,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};Ze&&Ze();We();_.t("gapi.config.get",_.Xe);_.t("gapi.config.update",_.Ye);
_.$e=function(a){a=_.we(a);return _.ec(a)};
var ff,gf,hf,jf,lf,nf,of,pf,qf,rf,sf,tf,uf,vf,wf,xf,yf,zf,Af,Bf,Df,Gf,Hf,If,Jf,Kf,Lf,Mf,Nf,Of,Pf,Sf,Tf;hf=void 0;jf=function(a){try{return _.Xa.JSON.parse.call(_.Xa.JSON,a)}catch(b){return!1}};lf=function(a){return Object.prototype.toString.call(a)};nf=lf(0);of=lf(new Date(0));pf=lf(!0);qf=lf("");rf=lf({});sf=lf([]);
tf=function(a,b){if(b)for(var c=0,d=b.length;c<d;++c)if(a===b[c])throw new TypeError("Converting circular structure to JSON");d=typeof a;if(d!=="undefined"){c=Array.prototype.slice.call(b||[],0);c[c.length]=a;b=[];var e=lf(a);if(a!=null&&typeof a.toJSON==="function"&&(Object.prototype.hasOwnProperty.call(a,"toJSON")||(e!==sf||a.constructor!==Array&&a.constructor!==Object)&&(e!==rf||a.constructor!==Array&&a.constructor!==Object)&&e!==qf&&e!==nf&&e!==pf&&e!==of))return tf(a.toJSON.call(a),c);if(a==
null)b[b.length]="null";else if(e===nf)a=Number(a),isNaN(a)||isNaN(a-a)?a="null":a===-0&&1/a<0&&(a="-0"),b[b.length]=String(a);else if(e===pf)b[b.length]=String(!!Number(a));else{if(e===of)return tf(a.toISOString.call(a),c);if(e===sf&&lf(a.length)===nf){b[b.length]="[";var f=0;for(d=Number(a.length)>>0;f<d;++f)f&&(b[b.length]=","),b[b.length]=tf(a[f],c)||"null";b[b.length]="]"}else if(e==qf&&lf(a.length)===nf){b[b.length]='"';f=0;for(c=Number(a.length)>>0;f<c;++f)d=String.prototype.charAt.call(a,
f),e=String.prototype.charCodeAt.call(a,f),b[b.length]=d==="\b"?"\\b":d==="\f"?"\\f":d==="\n"?"\\n":d==="\r"?"\\r":d==="\t"?"\\t":d==="\\"||d==='"'?"\\"+d:e<=31?"\\u"+(e+65536).toString(16).substr(1):e>=32&&e<=65535?d:"\ufffd";b[b.length]='"'}else if(d==="object"){b[b.length]="{";d=0;for(f in a)Object.prototype.hasOwnProperty.call(a,f)&&(e=tf(a[f],c),e!==void 0&&(d++&&(b[b.length]=","),b[b.length]=tf(f),b[b.length]=":",b[b.length]=e));b[b.length]="}"}else return}return b.join("")}};uf=/[\0-\x07\x0b\x0e-\x1f]/;
vf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*[\0-\x1f]/;wf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\[^\\\/"bfnrtu]/;xf=/^([^"]*"([^\\"]|\\.)*")*[^"]*"([^"\\]|\\.)*\\u([0-9a-fA-F]{0,3}[^0-9a-fA-F])/;yf=/"([^\0-\x1f\\"]|\\[\\\/"bfnrt]|\\u[0-9a-fA-F]{4})*"/g;zf=/-?(0|[1-9][0-9]*)(\.[0-9]+)?([eE][-+]?[0-9]+)?/g;Af=/[ \t\n\r]+/g;Bf=/[^"]:/;Df=/""/g;Gf=/true|false|null/g;Hf=/00/;If=/[\{]([^0\}]|0[^:])/;Jf=/(^|\[)[,:]|[,:](\]|\}|[,:]|$)/;Kf=/[^\[,:][\[\{]/;Lf=/^(\{|\}|\[|\]|,|:|0)+/;Mf=/\u2028/g;
Nf=/\u2029/g;
Of=function(a){a=String(a);if(uf.test(a)||vf.test(a)||wf.test(a)||xf.test(a))return!1;var b=a.replace(yf,'""');b=b.replace(zf,"0");b=b.replace(Af,"");if(Bf.test(b))return!1;b=b.replace(Df,"0");b=b.replace(Gf,"0");if(Hf.test(b)||If.test(b)||Jf.test(b)||Kf.test(b)||!b||(b=b.replace(Lf,"")))return!1;a=a.replace(Mf,"\\u2028").replace(Nf,"\\u2029");b=void 0;try{b=hf?[jf(a)]:eval("(function (var_args) {\n  return Array.prototype.slice.call(arguments, 0);\n})(\n"+a+"\n)")}catch(c){return!1}return b&&b.length===
1?b[0]:!1};Pf=function(){var a=((_.Xa.document||{}).scripts||[]).length;if((ff===void 0||hf===void 0||gf!==a)&&gf!==-1){ff=hf=!1;gf=-1;try{try{hf=!!_.Xa.JSON&&_.Xa.JSON.stringify.call(_.Xa.JSON,{a:[3,!0,new Date(0)],c:function(){}})==='{"a":[3,true,"1970-01-01T00:00:00.000Z"]}'&&jf("true")===!0&&jf('[{"a":3}]')[0].a===3}catch(b){}ff=hf&&!jf("[00]")&&!jf('"\u0007"')&&!jf('"\\0"')&&!jf('"\\v"')}finally{gf=a}}};_.Qf=function(a){if(gf===-1)return!1;Pf();return(ff?jf:Of)(a)};
_.Rf=function(a){if(gf!==-1)return Pf(),hf?_.Xa.JSON.stringify.call(_.Xa.JSON,a):tf(a)};Sf=!Date.prototype.toISOString||typeof Date.prototype.toISOString!=="function"||(new Date(0)).toISOString()!=="1970-01-01T00:00:00.000Z";
Tf=function(){var a=Date.prototype.getUTCFullYear.call(this);return[a<0?"-"+String(1E6-a).substr(1):a<=9999?String(1E4+a).substr(1):"+"+String(1E6+a).substr(1),"-",String(101+Date.prototype.getUTCMonth.call(this)).substr(1),"-",String(100+Date.prototype.getUTCDate.call(this)).substr(1),"T",String(100+Date.prototype.getUTCHours.call(this)).substr(1),":",String(100+Date.prototype.getUTCMinutes.call(this)).substr(1),":",String(100+Date.prototype.getUTCSeconds.call(this)).substr(1),".",String(1E3+Date.prototype.getUTCMilliseconds.call(this)).substr(1),
"Z"].join("")};Date.prototype.toISOString=Sf?Tf:Date.prototype.toISOString;
var Mg=function(){this.blockSize=-1},Ng=function(){this.blockSize=-1;this.blockSize=64;this.Rc=[];this.YE=[];this.E6=[];this.OB=[];this.OB[0]=128;for(var a=1;a<this.blockSize;++a)this.OB[a]=0;this.ED=this.fr=0;this.reset()};_.eb(Ng,Mg);Ng.prototype.reset=function(){this.Rc[0]=1732584193;this.Rc[1]=4023233417;this.Rc[2]=2562383102;this.Rc[3]=271733878;this.Rc[4]=3285377520;this.ED=this.fr=0};
var Og=function(a,b,c){c||(c=0);var d=a.E6;if(typeof b==="string")for(var e=0;e<16;e++)d[e]=b.charCodeAt(c)<<24|b.charCodeAt(c+1)<<16|b.charCodeAt(c+2)<<8|b.charCodeAt(c+3),c+=4;else for(e=0;e<16;e++)d[e]=b[c]<<24|b[c+1]<<16|b[c+2]<<8|b[c+3],c+=4;for(b=16;b<80;b++)c=d[b-3]^d[b-8]^d[b-14]^d[b-16],d[b]=(c<<1|c>>>31)&4294967295;b=a.Rc[0];c=a.Rc[1];e=a.Rc[2];for(var f=a.Rc[3],h=a.Rc[4],k,l,m=0;m<80;m++)m<40?m<20?(k=f^c&(e^f),l=1518500249):(k=c^e^f,l=1859775393):m<60?(k=c&e|f&(c|e),l=2400959708):(k=c^
e^f,l=3395469782),k=(b<<5|b>>>27)+k+h+l+d[m]&4294967295,h=f,f=e,e=(c<<30|c>>>2)&4294967295,c=b,b=k;a.Rc[0]=a.Rc[0]+b&4294967295;a.Rc[1]=a.Rc[1]+c&4294967295;a.Rc[2]=a.Rc[2]+e&4294967295;a.Rc[3]=a.Rc[3]+f&4294967295;a.Rc[4]=a.Rc[4]+h&4294967295};
Ng.prototype.update=function(a,b){if(a!=null){b===void 0&&(b=a.length);for(var c=b-this.blockSize,d=0,e=this.YE,f=this.fr;d<b;){if(f==0)for(;d<=c;)Og(this,a,d),d+=this.blockSize;if(typeof a==="string")for(;d<b;){if(e[f]=a.charCodeAt(d),++f,++d,f==this.blockSize){Og(this,e);f=0;break}}else for(;d<b;)if(e[f]=a[d],++f,++d,f==this.blockSize){Og(this,e);f=0;break}}this.fr=f;this.ED+=b}};
Ng.prototype.digest=function(){var a=[],b=this.ED*8;this.fr<56?this.update(this.OB,56-this.fr):this.update(this.OB,this.blockSize-(this.fr-56));for(var c=this.blockSize-1;c>=56;c--)this.YE[c]=b&255,b/=256;Og(this,this.YE);for(c=b=0;c<5;c++)for(var d=24;d>=0;d-=8)a[b]=this.Rc[c]>>d&255,++b;return a};_.Pg=function(){this.bN=new Ng};_.g=_.Pg.prototype;_.g.reset=function(){this.bN.reset()};_.g.C1=function(a){this.bN.update(a)};_.g.ZQ=function(){return this.bN.digest()};_.g.ux=function(a){a=unescape(encodeURIComponent(a));for(var b=[],c=a.length,d=0;d<c;++d)b.push(a.charCodeAt(d));this.C1(b)};_.g.Si=function(){for(var a=this.ZQ(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b};
_.di=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};_.ei=function(a){var b=_.di();if(!a)return b;a=a.split("/");for(var c=0,d=a.length;b&&typeof b==="object"&&c<d;++c)b=b[a[c]];return c===a.length&&b!==void 0?b:void 0};
_.fi=function(a,b,c,d){for(var e=0,f=a.length,h;e<f;){var k=e+(f-e>>>1);var l=c?b.call(void 0,a[k],k,a):b(d,a[k]);l>0?e=k+1:(f=k,h=!l)}return h?e:-e-1};_.gi=function(a,b){var c={},d;for(d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c};var hi;hi=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/u\/(\d)\//;
_.ii=function(a){var b=_.ei("googleapis.config/sessionIndex");"string"===typeof b&&b.length>254&&(b=null);b==null&&(b=window.__X_GOOG_AUTHUSER);"string"===typeof b&&b.length>254&&(b=null);if(b==null){var c=window.google;c&&(b=c.authuser)}"string"===typeof b&&b.length>254&&(b=null);b==null&&(a=a||window.location.href,b=_.Ge(a,"authuser")||null,b==null&&(b=(b=a.match(hi))?b[1]:null));if(b==null)return null;b=String(b);b.length>254&&(b=null);return b};
_.vi=function(){if(!_.Xa.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.Xa.addEventListener("test",c,b);_.Xa.removeEventListener("test",c,b)}catch(d){}return a}();
var wi=function(){var a=_.Me.ms||_.Me.u;if(a)return(new URL(a)).origin};var Di=function(a){this.KS=a;this.count=this.count=0};Di.prototype.rb=function(a,b){a?this.count+=a:this.count++;this.KS&&(b===void 0||b)&&this.KS()};Di.prototype.get=function(){return this.count};Di.prototype.reset=function(){this.count=0};var Fi,Ii;Fi=function(){var a=!0,b=this;a=a===void 0?!0:a;this.Ny=new Map;this.RE=!1;var c=wi();c&&(this.url=c+"/js/gen_204",c=_.ei("gen204logger")||{},this.ju=c.interval,this.LS=c.rate,this.RE=c.cqa,a&&this.url&&Ei(this),document.addEventListener("visibilitychange",this.flush),this.flush(),document.addEventListener("visibilitychange",function(){document.visibilityState==="hidden"&&b.flush()}),document.addEventListener("pagehide",this.flush.bind(this)))};_.Gi=function(){Fi.NW||(Fi.NW=new Fi);return Fi.NW};
Ii=function(a){var b=_.Me.dm||[];if(b&&b.length!==0){b=_.Aa(b);for(var c=b.next();!c.done;c=b.next())_.Hi(a,c.value).rb(1,!1);delete _.Me.dm;a.flush()}};_.Hi=function(a,b){a.Ny.has(b)||a.Ny.set(b,new Di(a.RE?void 0:function(){a.flush()}));return a.Ny.get(b)};
Fi.prototype.flush=function(){var a=this;if(this.url&&this.LS){Ii(this);for(var b="",c=_.Aa(this.Ny),d=c.next();!d.done;d=c.next()){var e=_.Aa(d.value);d=e.next().value;e=e.next().value;var f=e.get();f>0&&(b+=b.length>0?"&":"",b+="c=",b+=encodeURIComponent(d+":"+f),e.reset());if(b.length>1E3)break}if(b!==""&&Math.random()<this.LS){try{var h=AbortSignal.timeout(3E4)}catch(k){h=void 0}fetch(this.url+"?"+b,{method:"GET",mode:"no-cors",signal:h}).catch(function(){}).finally(function(){Ei(a)})}}};
Fi.prototype.setInterval=function(a){this.ju=a};var Ei=function(a){a.ju&&a.RE&&setTimeout(function(){a.flush()},a.ju)};var Ki,Ji,Qi,Ri,Li,Oi,Mi,Si,Ni;_.Pi=function(){_.Hi(_.Gi(),50).rb();if(Ji){var a=new _.xe.Uint32Array(1);Ki.getRandomValues(a);a=Number("0."+a[0])}else a=Li,a+=parseInt(Mi.substr(0,20),16),Mi=Ni(Mi),a/=Oi+1.2089258196146292E24;return a};Ki=_.xe.crypto;Ji=!1;Qi=0;Ri=0;Li=1;Oi=0;Mi="";Si=function(a){a=a||_.xe.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Li=Li*b%Oi;Qi>0&&++Ri==Qi&&_.Ke(_.xe,"mousemove",Si,"remove","de")};
Ni=function(a){var b=new _.Pg;b.ux(a);return b.Si()};Ji=!!Ki&&typeof Ki.getRandomValues=="function";Ji||(Oi=(screen.width*screen.width+screen.height)*1E6,Mi=Ni(_.ye.cookie+"|"+_.ye.location+"|"+(new Date).getTime()+"|"+Math.random()),Qi=_.ei("random/maxObserveMousemove")||0,Qi!=0&&_.Le(_.xe,"mousemove",Si));
var Ll,Ml,Nl,Ol,Pl,Ql,Rl,Sl,Tl,Ul,Xl,Yl,bm,cm,dm,em,fm,gm,hm,im;_.Kl=function(a,b){if(!a)throw Error(b||"");};Ll=/&/g;Ml=/</g;Nl=/>/g;Ol=/"/g;Pl=/'/g;Ql=function(a){return String(a).replace(Ll,"&amp;").replace(Ml,"&lt;").replace(Nl,"&gt;").replace(Ol,"&quot;").replace(Pl,"&#39;")};Rl=/[\ud800-\udbff][\udc00-\udfff]|[^!-~]/g;Sl=/%([a-f]|[0-9a-fA-F][a-f])/g;Tl=/^(https?|ftp|file|chrome-extension):$/i;
Ul=function(a){a=String(a);a=a.replace(Rl,function(e){try{return encodeURIComponent(e)}catch(f){return encodeURIComponent(e.replace(/^[^%]+$/g,"\ufffd"))}}).replace(_.Ie,function(e){return e.replace(/%/g,"%25")}).replace(Sl,function(e){return e.toUpperCase()});a=a.match(_.He)||[];var b=_.Ce(),c=function(e){return e.replace(/\\/g,"%5C").replace(/\^/g,"%5E").replace(/`/g,"%60").replace(/\{/g,"%7B").replace(/\|/g,"%7C").replace(/\}/g,"%7D")},d=!!(a[1]||"").match(Tl);b.mt=c((a[1]||"")+(a[2]||"")+(a[3]||
(a[2]&&d?"/":"")));d=function(e){return c(e.replace(/\?/g,"%3F").replace(/#/g,"%23"))};b.query=a[5]?[d(a[5])]:[];b.Xi=a[7]?[d(a[7])]:[];return b};Xl=function(a){return a.mt+(a.query.length>0?"?"+a.query.join("&"):"")+(a.Xi.length>0?"#"+a.Xi.join("&"):"")};Yl=function(a,b){var c=[];if(a)for(var d in a)if(_.De(a,d)&&a[d]!=null){var e=b?b(a[d]):a[d];c.push(encodeURIComponent(d)+"="+encodeURIComponent(e))}return c};
_.Zl=function(a,b,c,d){a=Ul(a);a.query.push.apply(a.query,Yl(b,d));a.Xi.push.apply(a.Xi,Yl(c,d));return Xl(a)};
_.$l=function(a,b){var c=Ul(b);b=c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));var d="";b.length>2E3&&(c=b,b=b.substr(0,2E3),b=b.replace(_.Je,""),d=c.substr(b.length));var e=a.createElement("div");a=a.createElement("a");c=Ul(b);b=c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));_.Ec(a,new _.lc(_.we(b)));e.appendChild(a);_.Hc(e,_.ec(e.innerHTML));b=String(e.firstChild.href);e.parentNode&&e.parentNode.removeChild(e);c=Ul(b+d);b=
c.mt;c.query.length&&(b+="?"+c.query.join(""));c.Xi.length&&(b+="#"+c.Xi.join(""));return b};_.am=/^https?:\/\/[^\/%\\?#\s]+\/[^\s]*$/i;cm=function(a){for(;a.firstChild;)a.removeChild(a.firstChild)};dm=/^https?:\/\/(?:\w|[\-\.])+\.google\.(?:\w|[\-:\.])+(?:\/[^\?#]*)?\/b\/(\d{10,21})\//;
em=function(){var a=_.ei("googleapis.config/sessionDelegate");"string"===typeof a&&a.length>21&&(a=null);a==null&&(a=(a=window.location.href.match(dm))?a[1]:null);if(a==null)return null;a=String(a);a.length>21&&(a=null);return a};fm=function(){var a=_.Me.onl;if(!a){a=_.Ce();_.Me.onl=a;var b=_.Ce();a.e=function(c){var d=b[c];d&&(delete b[c],d())};a.a=function(c,d){b[c]=d};a.r=function(c){delete b[c]}}return a};gm=function(a,b){b=b.onload;return typeof b==="function"?(fm().a(a,b),b):null};
hm=function(a){_.Kl(/^\w+$/.test(a),"Unsupported id - "+a);return'onload="window.___jsl.onl.e(&#34;'+a+'&#34;)"'};im=function(a){fm().r(a)};var km,lm,pm;_.jm={allowtransparency:"true",frameborder:"0",hspace:"0",marginheight:"0",marginwidth:"0",scrolling:"no",style:"",tabindex:"0",vspace:"0",width:"100%"};km={allowtransparency:!0,onload:!0};lm=0;_.mm=function(a,b){var c=0;do var d=b.id||["I",lm++,"_",(new Date).getTime()].join("");while(a.getElementById(d)&&++c<5);_.Kl(c<5,"Error creating iframe id");return d};_.nm=function(a,b){return a?b+"/"+a:""};
_.om=function(a,b,c,d){var e={},f={};a.documentMode&&a.documentMode<9&&(e.hostiemode=a.documentMode);_.Ee(d.queryParams||{},e);_.Ee(d.fragmentParams||{},f);var h=d.pfname;var k=_.Ce();_.ei("iframes/dropLegacyIdParam")||(k.id=c);k._gfid=c;k.parent=a.location.protocol+"//"+a.location.host;c=_.Ge(a.location.href,"parent");h=h||"";!h&&c&&(h=_.Ge(a.location.href,"_gfid","")||_.Ge(a.location.href,"id",""),h=_.nm(h,_.Ge(a.location.href,"pfname","")));h||(c=_.Qf(_.Ge(a.location.href,"jcp","")))&&typeof c==
"object"&&(h=_.nm(c.id,c.pfname));k.pfname=h;d.connectWithJsonParam&&(h={},h.jcp=_.Rf(k),k=h);h=_.Ge(b,"rpctoken")||e.rpctoken||f.rpctoken;h||(h=d.rpctoken||String(Math.round(_.Pi()*1E8)),k.rpctoken=h);d.rpctoken=h;_.Ee(k,d.connectWithQueryParams?e:f);k=a.location.href;a=_.Ce();(h=_.Ge(k,"_bsh",_.Me.bsh))&&(a._bsh=h);(k=_.Me.dpo?_.Me.h:_.Ge(k,"jsh",_.Me.h))&&(a.jsh=k);d.hintInFragment?_.Ee(a,f):_.Ee(a,e);return _.Zl(b,e,f,d.paramsSerializer)};
pm=function(a){_.Kl(!a||_.am.test(a),"Illegal url for new iframe - "+a)};
_.qm=function(a,b,c,d,e){pm(c.src);var f,h=gm(d,c),k=h?hm(d):"";try{document.all&&(f=a.createElement('<iframe frameborder="'+Ql(String(c.frameborder))+'" scrolling="'+Ql(String(c.scrolling))+'" '+k+' name="'+Ql(String(c.name))+'"/>'))}catch(m){}finally{f||(f=_.ae(a).createElement("IFRAME"),h&&(f.onload=function(){f.onload=null;h.call(this)},im(d)))}f.setAttribute("ng-non-bindable","");for(var l in c)a=c[l],l==="style"&&typeof a==="object"?_.Ee(a,f.style):km[l]||f.setAttribute(l,String(a));(l=e&&e.beforeNode||
null)||e&&e.dontclear||cm(b);b.insertBefore(f,l);f=l?l.previousSibling:b.lastChild;c.allowtransparency&&(f.allowTransparency=!0);return f};var rm,um;rm=/^:[\w]+$/;_.sm=/:([a-zA-Z_]+):/g;_.tm=function(){var a=_.ii()||"0",b=em();var c=_.ii()||a;var d=em(),e="";c&&(e+="u/"+encodeURIComponent(String(c))+"/");d&&(e+="b/"+encodeURIComponent(String(d))+"/");c=e||null;(e=(d=_.ei("isLoggedIn")===!1)?"_/im/":"")&&(c="");var f=_.ei("iframes/:socialhost:"),h=_.ei("iframes/:im_socialhost:");return bm={socialhost:f,ctx_socialhost:d?h:f,session_index:a,session_delegate:b,session_prefix:c,im_prefix:e}};um=function(a,b){return _.tm()[b]||""};
_.vm=function(a){return _.$l(_.ye,a.replace(_.sm,um))};_.wm=function(a){var b=a;rm.test(a)&&(b="iframes/"+b.substring(1)+"/url",b=_.ei(b),_.Kl(!!b,"Unknown iframe url config for - "+a));return _.vm(b)};
_.xm=function(a,b,c){c=c||{};var d=c.attributes||{};_.Kl(!(c.allowPost||c.forcePost)||!d.onload,"onload is not supported by post iframe (allowPost or forcePost)");a=_.wm(a);d=b.ownerDocument||_.ye;var e=_.mm(d,c);a=_.om(d,a,e,c);var f=c,h=_.Ce();_.Ee(_.jm,h);_.Ee(f.attributes,h);h.name=h.id=e;h.src=a;c.eurl=a;c=(f=c)||{};var k=!!c.allowPost;if(c.forcePost||k&&a.length>2E3){c=Ul(a);h.src="";f.dropDataPostorigin||(h["data-postorigin"]=a);a=_.qm(d,b,h,e);if(navigator.userAgent.indexOf("WebKit")!=-1){var l=
a.contentWindow.document;l.open();h=l.createElement("div");k={};var m=e+"_inner";k.name=m;k.src="";k.style="display:none";_.qm(d,h,k,m,f)}h=(f=c.query[0])?f.split("&"):[];f=[];for(k=0;k<h.length;k++)m=h[k].split("=",2),f.push([decodeURIComponent(m[0]),decodeURIComponent(m[1])]);c.query=[];h=Xl(c);_.Kl(_.am.test(h),"Invalid URL: "+h);c=d.createElement("form");c.method="POST";c.target=e;c.style.display="none";e=_.sc(h);e!==void 0&&(c.action=e);for(e=0;e<f.length;e++)h=d.createElement("input"),h.type=
"hidden",h.name=f[e][0],h.value=f[e][1],c.appendChild(h);b.appendChild(c);c.submit();c.parentNode.removeChild(c);l&&l.close();b=a}else b=_.qm(d,b,h,e,f);return b};
var Uf=function(){this.Eg=window.console};Uf.prototype.log=function(a){this.Eg&&this.Eg.log&&this.Eg.log(a)};Uf.prototype.error=function(a){this.Eg&&(this.Eg.error?this.Eg.error(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.warn=function(a){this.Eg&&(this.Eg.warn?this.Eg.warn(a):this.Eg.log&&this.Eg.log(a))};Uf.prototype.debug=function(){};_.Vf=new Uf;
_.Ig=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("s`"+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&c!=="moz-extension"&&
c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("t`"+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};
var th;_.sh=function(a){_.Xa.setTimeout(function(){throw a;},0)};th=0;_.uh=function(a){return Object.prototype.hasOwnProperty.call(a,_.ab)&&a[_.ab]||(a[_.ab]=++th)};
_.vh=function(){return _.Mc("Safari")&&!(_.Wc()||(_.Oc()?0:_.Mc("Coast"))||_.Pc()||_.Sc()||_.Tc()||_.Uc()||_.Vc()||_.Mc("Silk")||_.Mc("Android"))};_.wh=function(){return _.Mc("Android")&&!(_.Wc()||_.Vc()||_.Pc()||_.Mc("Silk"))};_.yh=_.Vc();_.zh=_.ad()||_.Mc("iPod");_.Ah=_.Mc("iPad");_.Bh=_.wh();_.Ch=_.Wc();_.Dh=_.vh()&&!_.bd();
var hj;_.gj=function(a,b){b=(0,_.sb)(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c};_.ij=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<hj.length;f++)c=hj[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};hj="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.jj=[];_.kj=[];_.lj=!1;
_.mj=function(a){_.jj[_.jj.length]=a;if(_.lj)for(var b=0;b<_.kj.length;b++)a((0,_.z)(_.kj[b].wrap,_.kj[b]))};
var bk=function(a){this.T=a};_.g=bk.prototype;_.g.value=function(){return this.T};_.g.Ne=function(a){this.T.width=a;return this};_.g.Qb=function(){return this.T.width};_.g.Td=function(a){this.T.height=a;return this};_.g.Nc=function(){return this.T.height};_.g.Ei=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.ck=function(a){this.T=a||{}};_.g=_.ck.prototype;_.g.value=function(){return this.T};_.g.setUrl=function(a){this.T.url=a;return this};_.g.getUrl=function(){return this.T.url};_.g.Ei=function(a){this.T.style=a;return this};_.g.getStyle=function(){return this.T.style};_.g.Me=function(a){this.T.id=a;return this};_.g.getId=function(){return this.T.id};_.g.Zm=function(a){this.T.rpctoken=a;return this};_.dk=function(a,b){a.T.messageHandlers=b;return a};_.ek=function(a,b){a.T.messageHandlersFilter=b;return a};
_.g=_.ck.prototype;_.g.Wr=_.jb(4);_.g.getContext=function(){return this.T.context};_.g.kd=function(){return this.T.openerIframe};_.g.Vn=function(){this.T.attributes=this.T.attributes||{};return new bk(this.T.attributes)};_.g.Fz=_.jb(5);
var jk;_.fk=function(a){var b={},c;for(c in a)b[c]=a[c];return b};jk=function(){for(var a;a=gk.remove();){try{a.Rh.call(a.scope)}catch(b){_.sh(b)}hk.put(a)}ik=!1};_.kk=function(a){if(!(a instanceof Array)){a=_.Aa(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};_.lk=function(){};_.mk=function(a){a.prototype.$goog_Thenable=!0};_.nk=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};
_.ok=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var pk=function(a,b){this.l8=a;this.afa=b;this.AB=0;this.AA=null};pk.prototype.get=function(){if(this.AB>0){this.AB--;var a=this.AA;this.AA=a.next;a.next=null}else a=this.l8();return a};pk.prototype.put=function(a){this.afa(a);this.AB<100&&(this.AB++,a.next=this.AA,this.AA=a)};_.qk=function(a){return a};_.mj(function(a){_.qk=a});var rk=function(){this.WD=this.Os=null};rk.prototype.add=function(a,b){var c=hk.get();c.set(a,b);this.WD?this.WD.next=c:this.Os=c;this.WD=c};rk.prototype.remove=function(){var a=null;this.Os&&(a=this.Os,this.Os=this.Os.next,this.Os||(this.WD=null),a.next=null);return a};var hk=new pk(function(){return new sk},function(a){return a.reset()}),sk=function(){this.next=this.scope=this.Rh=null};sk.prototype.set=function(a,b){this.Rh=a;this.scope=b;this.next=null};
sk.prototype.reset=function(){this.next=this.scope=this.Rh=null};var tk,ik,gk,uk;ik=!1;gk=new rk;_.vk=function(a,b){tk||uk();ik||(tk(),ik=!0);gk.add(a,b)};uk=function(){var a=Promise.resolve(void 0);tk=function(){a.then(jk)}};var yk,zk,Ak,Ok,Sk,Qk,Tk;_.xk=function(a,b){this.Ca=0;this.qf=void 0;this.cq=this.Bl=this.Gb=null;this.qA=this.fG=!1;if(a!=_.lk)try{var c=this;a.call(b,function(d){wk(c,2,d)},function(d){wk(c,3,d)})}catch(d){wk(this,3,d)}};yk=function(){this.next=this.context=this.Er=this.Ov=this.xn=null;this.Ux=!1};yk.prototype.reset=function(){this.context=this.Er=this.Ov=this.xn=null;this.Ux=!1};zk=new pk(function(){return new yk},function(a){a.reset()});
Ak=function(a,b,c){var d=zk.get();d.Ov=a;d.Er=b;d.context=c;return d};_.Bk=function(a){if(a instanceof _.xk)return a;var b=new _.xk(_.lk);wk(b,2,a);return b};_.Ck=function(a){return new _.xk(function(b,c){c(a)})};_.Ek=function(a,b,c){Dk(a,b,c,null)||_.vk(_.bb(b,a))};_.Fk=function(a){return new _.xk(function(b,c){var d=a.length,e=[];if(d)for(var f=function(m,n){d--;e[m]=n;d==0&&b(e)},h=function(m){c(m)},k,l=0;l<a.length;l++)k=a[l],_.Ek(k,_.bb(f,l),h);else b(e)})};
_.Hk=function(){var a,b,c=new _.xk(function(d,e){a=d;b=e});return new Gk(c,a,b)};_.xk.prototype.then=function(a,b,c){return Ik(this,(0,_.ok)(typeof a==="function"?a:null),(0,_.ok)(typeof b==="function"?b:null),c)};_.mk(_.xk);var Kk=function(a,b,c,d){Jk(a,Ak(b||_.lk,c||null,d))};_.xk.prototype.finally=function(a){var b=this;a=(0,_.ok)(a);return new Promise(function(c,d){Kk(b,function(e){a();c(e)},function(e){a();d(e)})})};_.xk.prototype.zD=function(a,b){return Ik(this,null,(0,_.ok)(a),b)};
_.xk.prototype.catch=_.xk.prototype.zD;_.xk.prototype.cancel=function(a){if(this.Ca==0){var b=new _.Lk(a);_.vk(function(){Mk(this,b)},this)}};
var Mk=function(a,b){if(a.Ca==0)if(a.Gb){var c=a.Gb;if(c.Bl){for(var d=0,e=null,f=null,h=c.Bl;h&&(h.Ux||(d++,h.xn==a&&(e=h),!(e&&d>1)));h=h.next)e||(f=h);e&&(c.Ca==0&&d==1?Mk(c,b):(f?(d=f,d.next==c.cq&&(c.cq=d),d.next=d.next.next):Nk(c),Ok(c,e,3,b)))}a.Gb=null}else wk(a,3,b)},Jk=function(a,b){a.Bl||a.Ca!=2&&a.Ca!=3||Pk(a);a.cq?a.cq.next=b:a.Bl=b;a.cq=b},Ik=function(a,b,c,d){var e=Ak(null,null,null);e.xn=new _.xk(function(f,h){e.Ov=b?function(k){try{var l=b.call(d,k);f(l)}catch(m){h(m)}}:f;e.Er=c?
function(k){try{var l=c.call(d,k);l===void 0&&k instanceof _.Lk?h(k):f(l)}catch(m){h(m)}}:h});e.xn.Gb=a;Jk(a,e);return e.xn};_.xk.prototype.sha=function(a){this.Ca=0;wk(this,2,a)};_.xk.prototype.tha=function(a){this.Ca=0;wk(this,3,a)};
var wk=function(a,b,c){a.Ca==0&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.Ca=1,Dk(c,a.sha,a.tha,a)||(a.qf=c,a.Ca=b,a.Gb=null,Pk(a),b!=3||c instanceof _.Lk||Qk(a,c)))},Dk=function(a,b,c,d){if(a instanceof _.xk)return Kk(a,b,c,d),!0;if(_.nk(a))return a.then(b,c,d),!0;if(_.vb(a))try{var e=a.then;if(typeof e==="function")return Rk(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Rk=function(a,b,c,d,e){var f=!1,h=function(l){f||(f=!0,c.call(e,l))},k=function(l){f||(f=!0,
d.call(e,l))};try{b.call(a,h,k)}catch(l){k(l)}},Pk=function(a){a.fG||(a.fG=!0,_.vk(a.nz,a))},Nk=function(a){var b=null;a.Bl&&(b=a.Bl,a.Bl=b.next,b.next=null);a.Bl||(a.cq=null);return b};_.xk.prototype.nz=function(){for(var a;a=Nk(this);)Ok(this,a,this.Ca,this.qf);this.fG=!1};Ok=function(a,b,c,d){if(c==3&&b.Er&&!b.Ux)for(;a&&a.qA;a=a.Gb)a.qA=!1;if(b.xn)b.xn.Gb=null,Sk(b,c,d);else try{b.Ux?b.Ov.call(b.context):Sk(b,c,d)}catch(e){Tk.call(null,e)}zk.put(b)};
Sk=function(a,b,c){b==2?a.Ov.call(a.context,c):a.Er&&a.Er.call(a.context,c)};Qk=function(a,b){a.qA=!0;_.vk(function(){a.qA&&Tk.call(null,b)})};Tk=_.sh;_.Lk=function(a){_.lb.call(this,a);this.rZ=!1};_.eb(_.Lk,_.lb);_.Lk.prototype.name="cancel";var Gk=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};
_.Uk=function(a){return new _.xk(a)};
var bl=function(){this.nx={lZ:Vk?"../"+Vk:null,Yy:Wk,cU:Xk,Yra:Yk,ho:Zk,Psa:$k};this.Vf=_.xe;this.CY=this.s8;this.m9=/MSIE\s*[0-8](\D|$)/.test(window.navigator.userAgent);if(this.nx.lZ){this.Vf=this.nx.cU(this.Vf,this.nx.lZ);var a=this.Vf.document,b=a.createElement("script");b.setAttribute("type","text/javascript");b.text="window.doPostMsg=function(w,s,o) {window.setTimeout(function(){w.postMessage(s,o);},0);};";a.body.appendChild(b);this.CY=this.Vf.doPostMsg}this.cN={};this.FN={};a=(0,_.z)(this.DH,
this);_.Le(this.Vf,"message",a);_.Be(_.Me,"RPMQ",[]).push(a);this.Vf!=this.Vf.parent&&al(this,this.Vf.parent,this.VI(this.Vf.name),"*")};bl.prototype.VI=function(a){return'{"h":"'+escape(a)+'"}'};var cl=function(a){var b=null;a.indexOf('{"h":"')===0&&a.indexOf('"}')===a.length-2&&(b=unescape(a.substring(6,a.length-2)));return b},dl=function(a){if(!/^\s*{/.test(a))return!1;a=_.Qf(a);return a!==null&&typeof a==="object"&&!!a.g};
bl.prototype.DH=function(a){var b=String(a.data);_.Vf.debug("gapix.rpc.receive("+Yk+"): "+(!b||b.length<=512?b:b.substr(0,512)+"... ("+b.length+" bytes)"));var c=b.indexOf("!_")!==0;c||(b=b.substring(2));var d=dl(b);if(!c&&!d){if(!d&&(c=cl(b))){if(this.cN[c])this.cN[c]();else this.FN[c]=1;return}var e=a.origin,f=this.nx.Yy;this.m9?_.xe.setTimeout(function(){f(b,e)},0):f(b,e)}};bl.prototype.Ib=function(a,b){a===".."||this.FN[a]?(b(),delete this.FN[a]):this.cN[a]=b};
var al=function(a,b,c,d){var e=dl(c)?"":"!_";_.Vf.debug("gapix.rpc.send("+Yk+"): "+(!c||c.length<=512?c:c.substr(0,512)+"... ("+c.length+" bytes)"));a.CY(b,e+c,d)};bl.prototype.s8=function(a,b,c){a.postMessage(b,c)};bl.prototype.send=function(a,b,c){(a=this.nx.cU(this.Vf,a))&&!a.closed&&al(this,a,b,c)};var el,fl,gl,hl,il,jl,kl,Vk,Yk,ll,ml,nl,Xk,Zk,pl,ql,Al,Bl,Dl,$k,Fl,El,rl,sl,Gl,Wk,Hl,Il;el=0;fl=[];gl={};hl={};il=_.xe.location.href;jl=_.Ge(il,"rpctoken");kl=_.Ge(il,"parent")||_.ye.referrer;Vk=_.Ge(il,"rly");Yk=Vk||(_.xe!==_.xe.top||_.xe.opener)&&_.xe.name||"..";ll=null;ml={};nl=function(){};_.ol={send:nl,Ib:nl,VI:nl};
Xk=function(a,b){var c=a;b.charAt(0)=="/"&&(b=b.substring(1),c=_.xe.top);if(b.length===0)return c;for(b=b.split("/");b.length;){a=b.shift();a.charAt(0)=="{"&&a.charAt(a.length-1)=="}"&&(a=a.substring(1,a.length-1));var d=a;if(d==="..")c=c==c.parent?c.opener:c.parent;else if(d!==".."&&c.frames[d]){var e=c;a=d;c=c.frames[d];if(!("postMessage"in c))if(c instanceof HTMLIFrameElement&&"contentWindow"in c)c=c.contentWindow!=null&&"postMessage"in c.contentWindow?c.contentWindow:null;else{d=null;e=_.Aa(e.document.getElementsByTagName("iframe"));
for(var f=e.next();!f.done;f=e.next())if(f=f.value,f.getAttribute("id")==a||f.getAttribute("name")==a)d=f;if(d&&"contentWindow"in d)c=d.contentWindow!=null?d.contentWindow:null;else throw Error("F`"+c+"`"+a);}}else return null}return c};Zk=function(a){return(a=gl[a])&&a.token};pl=function(a){if(a.f in{})return!1;var b=a.t,c=gl[a.r];a=a.origin;return c&&(c.token===b||!c.token&&!b)&&(a===c.origin||c.origin==="*")};
ql=function(a){var b=a.id.split("/"),c=b[b.length-1],d=a.origin;return function(e){var f=e.origin;return e.f==c&&(d==f||d=="*")}};_.tl=function(a,b,c){a=rl(a);hl[a.name]={Rh:b,Cv:a.Cv,Ms:c||pl};sl()};_.ul=function(a){a=rl(a);delete hl[a.name]};Al={};Bl=function(a,b){(a=Al["_"+a])&&a[1](this)&&a[0].call(this,b)};Dl=function(a){var b=a.c;if(!b)return nl;var c=a.r,d=a.g?"legacy__":"";return function(){var e=[].slice.call(arguments,0);e.unshift(c,d+"__cb",null,b);_.Cl.apply(null,e)}};
$k=function(a){ll=a};Fl=function(a){ml[a]||(ml[a]=_.xe.setTimeout(function(){ml[a]=!1;El(a)},0))};El=function(a){var b=gl[a];if(b&&b.ready){var c=b.iK;for(b.iK=[];c.length;)_.ol.send(a,_.Rf(c.shift()),b.origin)}};rl=function(a){return a.indexOf("legacy__")===0?{name:a.substring(8),Cv:!0}:{name:a,Cv:!1}};
sl=function(){for(var a=_.ei("rpc/residenceSec")||60,b=(new Date).getTime()/1E3,c,d=0;c=fl[d];++d){var e=c.kp;if(!e||a>0&&b-c.timestamp>a)fl.splice(d,1),--d;else{var f=e.s,h=hl[f]||hl["*"];if(h)if(fl.splice(d,1),--d,e.origin=c.origin,c=Dl(e),e.callback=c,h.Ms(e)){if(f!=="__cb"&&!!h.Cv!=!!e.g)break;e=h.Rh.apply(e,e.a);e!==void 0&&c(e)}else _.Vf.debug("gapix.rpc.rejected("+Yk+"): "+f)}}};Gl=function(a,b,c){fl.push({kp:a,origin:b,timestamp:(new Date).getTime()/1E3});c||sl()};
Wk=function(a,b){a=_.Qf(a);Gl(a,b,!1)};Hl=function(a){for(;a.length;)Gl(a.shift(),this.origin,!0);sl()};Il=function(a){var b=!1;a=a.split("|");var c=a[0];c.indexOf("/")>=0&&(b=!0);return{id:c,origin:a[1]||"*",rI:b}};
_.Jl=function(a,b,c,d){var e=Il(a);d&&(_.xe.frames[e.id]=_.xe.frames[e.id]||d);a=e.id;if(!gl.hasOwnProperty(a)){c=c||null;d=e.origin;if(a==="..")d=_.Ig(kl),c=c||jl;else if(!e.rI){var f=_.ye.getElementById(a);f&&(f=f.src,d=_.Ig(f),c=c||_.Ge(f,"rpctoken"))}e.origin==="*"&&d||(d=e.origin);gl[a]={token:c,iK:[],origin:d,mfa:b,dZ:function(){var h=a;gl[h].ready=1;El(h)}};_.ol.Ib(a,gl[a].dZ)}return gl[a].dZ};
_.Cl=function(a,b,c,d){a=a||"..";_.Jl(a);a=a.split("|",1)[0];var e=b,f=a,h=[].slice.call(arguments,3),k=c,l=Yk,m=jl,n=gl[f],p=l,q=Il(f);if(n&&f!==".."){if(q.rI){if(!(m=gl[f].mfa)){m=ll?ll.substring(1).split("/"):[Yk];p=m.length-1;for(f=_.xe.parent;f!==_.xe.top;){var r=f.parent;if(!p--){for(var w=null,u=r.frames.length,x=0;x<u;++x)r.frames[x]==f&&(w=x);m.unshift("{"+w+"}")}f=r}m="/"+m.join("/")}p=m}else p=l="..";m=n.token}k&&q?(n=pl,q.rI&&(n=ql(q)),Al["_"+ ++el]=[k,n],k=el):k=null;h={s:e,f:l,r:p,t:m,
c:k,a:h};e=rl(e);h.s=e.name;h.g=e.Cv;gl[a].iK.push(h);Fl(a)};if(typeof _.xe.postMessage==="function"||typeof _.xe.postMessage==="object")_.ol=new bl,_.tl("__cb",Bl,function(){return!0}),_.tl("_processBatch",Hl,function(){return!0}),_.Jl("..");
var ym;
ym=function(){function a(k,l){k=window.getComputedStyle(k,"").getPropertyValue(l).match(/^([0-9]+)/);return parseInt(k[0],10)}for(var b=0,c=[document.body];c.length>0;){var d=c.shift(),e=d.childNodes;if(typeof d.style!=="undefined"){var f=d.style.overflowY;f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.overflowY:null);if(f!="visible"&&f!="inherit"&&(f=d.style.height,f||(f=(f=document.defaultView.getComputedStyle(d,null))?f.height:""),f.length>0&&f!="auto"))continue}for(d=0;d<e.length;d++){f=e[d];
if(typeof f.offsetTop!=="undefined"&&typeof f.offsetHeight!=="undefined"){var h=f.offsetTop+f.offsetHeight+a(f,"margin-bottom");b=Math.max(b,h)}c.push(f)}}return b+a(document.body,"border-bottom")+a(document.body,"margin-bottom")+a(document.body,"padding-bottom")};
_.zm=function(){var a=0;self.innerHeight?a=self.innerHeight:document.documentElement&&document.documentElement.clientHeight?a=document.documentElement.clientHeight:document.body&&(a=document.body.clientHeight);var b=document.body,c=document.documentElement;if(document.compatMode==="CSS1Compat"&&c.scrollHeight)return c.scrollHeight!==a?c.scrollHeight:c.offsetHeight;if(navigator.userAgent.indexOf("AppleWebKit")>=0)return ym();if(b&&c){var d=c.scrollHeight,e=c.offsetHeight;c.clientHeight!==e&&(d=b.scrollHeight,
e=b.offsetHeight);return d>a?d>e?d:e:d<e?d:e}};
var Am=function(a,b){return _.fi(a,b,!0)},Bm=function(a){this.T=a||{}},Cm=function(a){var b=function(c){return new (a().Context)(c)};b.prototype.addOnConnectHandler=function(c,d,e,f){return a().Context.prototype.addOnConnectHandler.apply(this,[c,d,e,f])};b.prototype.addOnOpenerHandler=function(c,d,e){return a().Context.prototype.addOnOpenerHandler.apply(this,[c,d,e])};b.prototype.closeSelf=function(c,d,e){return a().Context.prototype.closeSelf.apply(this,[c,d,e])};b.prototype.connectIframes=function(c,
d){a().Context.prototype.connectIframes.apply(this,[c,d])};b.prototype.getFrameName=function(){return a().Context.prototype.getFrameName.apply(this)};b.prototype.getGlobalParam=function(c){a().Context.prototype.getGlobalParam.apply(this,[c])};b.prototype.getParentIframe=function(){return a().Context.prototype.getParentIframe.apply(this)};b.prototype.getWindow=function(){return a().Context.prototype.getWindow.apply(this)};b.prototype.isDisposed=function(){return a().Context.prototype.isDisposed.apply(this)};
b.prototype.open=function(c,d){return a().Context.prototype.open.apply(this,[c,d])};b.prototype.openChild=function(c){return a().Context.prototype.openChild.apply(this,[c])};b.prototype.ready=function(c,d,e,f){a().Context.prototype.ready.apply(this,[c,d,e,f])};b.prototype.removeOnConnectHandler=function(c){a().Context.prototype.removeOnConnectHandler.apply(this,[c])};b.prototype.restyleSelf=function(c,d,e){return a().Context.prototype.restyleSelf.apply(this,[c,d,e])};b.prototype.setCloseSelfFilter=
function(c){a().Context.prototype.setCloseSelfFilter.apply(this,[c])};b.prototype.setGlobalParam=function(c,d){a().Context.prototype.setGlobalParam.apply(this,[c,d])};b.prototype.setRestyleSelfFilter=function(c){a().Context.prototype.setRestyleSelfFilter.apply(this,[c])};return b},Dm=function(a){var b=function(c,d,e,f){return new (a().Iframe)(c,d,e,f)};b.prototype.applyIframesApi=function(c){a().Iframe.prototype.applyIframesApi(c)};b.prototype.close=function(c,d){return a().Iframe.prototype.close.apply(this,
[c,d])};b.prototype.getContext=function(){return a().Iframe.prototype.getContext.apply(this,[])};b.prototype.getFrameName=function(){return a().Iframe.prototype.getFrameName.apply(this,[])};b.prototype.getId=function(){return a().Iframe.prototype.getId.apply(this,[])};b.prototype.getIframeEl=function(){return a().Iframe.prototype.getIframeEl.apply(this,[])};b.prototype.getOrigin=function(){return a().Iframe.prototype.getOrigin.apply(this,[])};b.prototype.getParam=function(c){a().Iframe.prototype.getParam.apply(this,
[c])};b.prototype.getSiteEl=function(){return a().Iframe.prototype.getSiteEl.apply(this,[])};b.prototype.getWindow=function(){return a().Iframe.prototype.getWindow.apply(this,[])};b.prototype.isDisposed=function(){return a().Iframe.prototype.isDisposed.apply(this,[])};b.prototype.ping=function(c,d){return a().Iframe.prototype.ping.apply(this,[c,d])};b.prototype.register=function(c,d,e){a().Iframe.prototype.register.apply(this,[c,d,e])};b.prototype.registerWasClosed=function(c,d){a().Iframe.prototype.registerWasClosed.apply(this,
[c,d])};b.prototype.registerWasRestyled=function(c,d){a().Iframe.prototype.registerWasRestyled.apply(this,[c,d])};b.prototype.restyle=function(c,d){return a().Iframe.prototype.restyle.apply(this,[c,d])};b.prototype.send=function(c,d,e,f){return a().Iframe.prototype.send.apply(this,[c,d,e,f])};b.prototype.setParam=function(c,d){a().Iframe.prototype.setParam.apply(this,[c,d])};b.prototype.setSiteEl=function(c){a().Iframe.prototype.setSiteEl.apply(this,[c])};b.prototype.unregister=function(c,d){a().Iframe.prototype.unregister.apply(this,
[c,d])};return b},Em,Fm,Im,Km,Mm,Rm,Zm,$m,bn,fn,gn,kn,mn,nn,pn,on,qn;_.ck.prototype.Fz=_.pb(5,function(){return this.T.controller});_.ck.prototype.Wr=_.pb(4,function(a){this.T.apis=a;return this});Em=function(a,b){a.T.onload=b};Fm=function(a){return a.T.rpctoken};_.Gm=function(a,b){a.T.queryParams=b;return a};_.Hm=function(a,b){a.T.relayOpen=b;return a};Im=function(a){return a.T.apis};_.Jm=function(a,b){a.T.onClose=b;return a};Km=function(a,b){a.T.controllerData=b};
_.Lm=function(a){a.T.waitForOnload=!0};Mm=function(a){return(a=a.T.timeout)?a:null};_.Nm=function(a){return!!a&&typeof a==="object"&&_.Ae.test(a.push)};_.Om=function(a){for(var b=0;b<this.length;b++)if(this[b]===a)return b;return-1};_.Pm=function(a,b,c){if(a){_.Kl(_.Nm(a),"arrayForEach was called with a non array value");for(var d=0;d<a.length;d++)b.call(c,a[d],d)}};
_.Qm=function(a,b,c){if(a)if(_.Nm(a))_.Pm(a,b,c);else{_.Kl(typeof a==="object","objectForEach was called with a non object value");c=c||a;for(var d in a)_.De(a,d)&&a[d]!==void 0&&b.call(c,a[d],d)}};Rm=function(a){this.T=a||{}};Rm.prototype.value=function(){return this.T};Rm.prototype.getIframe=function(){return this.T.iframe};var Sm=function(a,b){a.T.role=b;return a},Tm=function(a,b){a.T.data=b;return a};Rm.prototype.Vk=function(a){this.T.setRpcReady=a;return this};var Um=function(a){return a.T.setRpcReady};
Rm.prototype.Zm=function(a){this.T.rpctoken=a;return this};var Vm=function(a){a.T.selfConnect=!0;return a};Bm.prototype.value=function(){return this.T};var Xm=function(a){var b=new Wm;b.T.role=a;return b};Bm.prototype.QT=function(){return this.T.role};Bm.prototype.Fc=function(a){this.T.handler=a;return this};Bm.prototype.wb=function(){return this.T.handler};var Ym=function(a,b){a.T.filter=b;return a};Bm.prototype.Wr=function(a){this.T.apis=a;return this};bn=/^[\w\.\-]*$/;
_.cn=function(a){return a.getOrigin()===a.getContext().getOrigin()};_.dn=function(){return!0};_.en=function(a){for(var b=_.Ce(),c=0;c<a.length;c++)b[a[c]]=!0;return function(d){return!!b[d.Cd]}};fn=function(a,b,c){a=Zm[a];if(!a)return[];for(var d=[],e=0;e<a.length;e++)d.push(_.Bk(a[e].call(c,b,c)));return d};gn=function(a,b,c){return function(d){if(!b.isDisposed()){var e=this.origin,f=b.getOrigin();_.Kl(e===f,"Wrong origin "+e+" != "+f);e=this.callback;d=fn(a,d,b);!c&&d.length>0&&_.Fk(d).then(e)}}};
_.hn=function(a,b,c){_.Kl(a!="_default","Cannot update default api");$m[a]={map:b,filter:c}};_.jn=function(a,b,c){_.Kl(a!="_default","Cannot update default api");_.Be($m,a,{map:{},filter:_.cn}).map[b]=c};kn=function(a,b){_.Be($m,"_default",{map:{},filter:_.dn}).map[a]=b;_.Qm(_.an.dg,function(c){c.register(a,b,_.dn)})};_.ln=function(){return _.an};mn=/^https?:\/\/[^\/%\\?#\s]+$/i;
nn={longdesc:!0,name:!0,src:!0,frameborder:!0,marginwidth:!0,marginheight:!0,scrolling:!0,align:!0,height:!0,width:!0,id:!0,"class":!0,title:!0,tabindex:!0,hspace:!0,vspace:!0,allowtransparency:!0};pn=function(a){this.resolve=this.reject=null;this.promise=_.Uk((0,_.z)(function(b,c){this.resolve=b;this.reject=c},this));a&&(this.promise=on(this.promise,a))};on=function(a,b){return a.then(function(c){try{b(c)}catch(d){}return c})};qn=function(a){this.ig=a;this.Context=Cm(a);this.Iframe=Dm(a)};_.g=qn.prototype;
_.g.CROSS_ORIGIN_IFRAMES_FILTER=function(a){return this.ig().CROSS_ORIGIN_IFRAMES_FILTER(a)};_.g.SAME_ORIGIN_IFRAMES_FILTER=function(a){return this.ig().SAME_ORIGIN_IFRAMES_FILTER(a)};_.g.create=function(a,b,c){return this.ig().create(a,b,c)};_.g.getBeforeOpenStyle=function(a){return this.ig().getBeforeOpenStyle(a)};_.g.getContext=function(){return this.ig().getContext()};_.g.getStyle=function(a){return this.ig().getStyle(a)};_.g.makeWhiteListIframesFilter=function(a){return this.ig().makeWhiteListIframesFilter(a)};
_.g.registerBeforeOpenStyle=function(a,b){return this.ig().registerBeforeOpenStyle(a,b)};_.g.registerIframesApi=function(a,b,c){return this.ig().registerIframesApi(a,b,c)};_.g.registerIframesApiHandler=function(a,b,c){return this.ig().registerIframesApiHandler(a,b,c)};_.g.registerStyle=function(a,b){return this.ig().registerStyle(a,b)};var rn=function(){this.yi=[]};rn.prototype.ig=function(a){return this.yi.length?sn(this.yi[0],a):void 0};var sn=function(a,b){b=b===void 0?function(c){return new c}:b;return a.ctor?b(a.ctor):a.instance},tn=function(){rn.apply(this,arguments)};_.y(tn,rn);var vn=function(a){var b=un.TQ,c=a.priority,d=~Am(b.yi,function(e){return e.priority<c?-1:1});b.yi.splice(d,0,a)};var un=new function(){var a=this;this.TQ=new tn;this.instance=new qn(function(){return a.TQ.ig()()})};vn({instance:function(){return window.gapi.iframes},priority:1});_.wn=un.instance;var xn,yn;xn={height:!0,width:!0};yn=/^(?!-*(?:expression|(?:moz-)?binding))(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|-?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:[a-z]{1,2}|%)?|!important|)$/i;_.zn=function(a){typeof a==="number"&&(a=String(a)+"px");return a};var An=function(){Rm.apply(this,arguments)};_.y(An,Rm);var Wm=function(){Bm.apply(this,arguments)};_.y(Wm,Bm);var Bn=function(){_.ck.apply(this,arguments)};_.y(Bn,_.ck);var Cn=function(a){Bn.call(this,a)};_.y(Cn,Bn);var Dn=function(a,b){a.T.frameName=b;return a};Cn.prototype.getFrameName=function(){return this.T.frameName};var En=function(a,b){a.T.rpcAddr=b;return a};Cn.prototype.og=function(){return this.T.rpcAddr};var Fn=function(a,b){a.T.retAddr=b;return a};Cn.prototype.Zh=function(){return this.T.retAddr};Cn.prototype.Mj=function(a){this.T.origin=a;return this};Cn.prototype.getOrigin=function(){return this.T.origin};
Cn.prototype.Vk=function(a){this.T.setRpcReady=a;return this};var Gn=function(a){return a.T._popupWindow};Cn.prototype.wp=function(a){this.T.context=a};var Hn=function(a,b){a.T._rpcReadyFn=b};Cn.prototype.getIframeEl=function(){return this.T.iframeEl};var In=function(a,b,c){var d=a.og(),e=b.Zh();Fn(En(c,a.Zh()+"/"+b.og()),e+"/"+d);Dn(c,b.getFrameName()).Mj(b.getOrigin())};var Kn=function(a,b,c){a.setTimeout(function(){b.closed||c==5?Jn(b):(b.close(),c++,Kn(a,b,c))},1E3)},Jn=function(a){a.closed||a.document&&a.document.body&&_.ve(a.document.body,"Please close this window.")};_.Ln=function(a,b,c,d){this.Lg=!1;this.qb=a;this.DK=b;this.uq=c;this.Ka=d;this.DZ=this.Ka.Zh();this.Cd=this.Ka.getOrigin();this.Bba=this.Ka.getIframeEl();this.u0=this.Ka.T.where;this.yi=[];this.applyIframesApi("_default");a=Im(this.Ka)||[];for(b=0;b<a.length;b++)this.applyIframesApi(a[b]);this.qb.dg[c]=this};_.g=_.Ln.prototype;_.g.isDisposed=function(){return this.Lg};
_.g.dispose=function(){if(!this.isDisposed()){for(var a=0;a<this.yi.length;a++)this.unregister(this.yi[a]);delete _.an.dg[this.getFrameName()];this.Lg=!0}};_.g.getContext=function(){return this.qb};_.g.getOptions=function(){return this.Ka};_.g.og=function(){return this.DK};_.g.Zh=function(){return this.DZ};_.g.getFrameName=function(){return this.uq};_.g.getIframeEl=function(){return this.Bba};_.g.getSiteEl=function(){return this.u0};_.g.setSiteEl=function(a){this.u0=a};_.g.Vk=function(){(0,this.Ka.T._rpcReadyFn)()};
_.g.setParam=function(a,b){this.Ka.value()[a]=b};_.g.getParam=function(a){return this.Ka.value()[a]};_.g.wc=function(){return this.Ka.value()};_.g.getId=function(){return this.Ka.getId()};_.g.getOrigin=function(){return this.Cd};var Mn=function(a,b){var c=a.uq;a=a.qb.getFrameName();return c+":"+a+":"+b};_.g=_.Ln.prototype;
_.g.register=function(a,b,c){_.Kl(!this.isDisposed(),"Cannot register handler on disposed iframe "+a);_.Kl((c||_.cn)(this),"Rejecting untrusted message "+a);c=Mn(this,a);_.Be(Zm,c,[]).push(b)==1&&(this.yi.push(a),_.tl(c,gn(c,this,a==="_g_wasClosed")))};_.g.unregister=function(a,b){var c=Mn(this,a),d=Zm[c];d&&(b?(b=_.Om.call(d,b),b>=0&&d.splice(b,1)):d.splice(0,d.length),d.length==0&&(b=_.Om.call(this.yi,a),b>=0&&this.yi.splice(b,1),_.ul(c)))};_.g.B$=function(){return this.yi};
_.g.applyIframesApi=function(a){this.HE=this.HE||[];if(!(_.Om.call(this.HE,a)>=0)){this.HE.push(a);a=$m[a]||{map:{}};for(var b in a.map)_.De(a.map,b)&&this.register(b,a.map[b],a.filter)}};_.g.getWindow=function(){if(!_.cn(this))return null;var a=Gn(this.Ka);if(a)return a;var b=this.DK.split("/");a=this.getContext().getWindow();for(var c=0;c<b.length&&a;c++){var d=b[c];a=".."===d?a==a.parent?a.opener:a.parent:a.frames[d]}return a};
var Nn=function(a){var b={};if(a)for(var c in a)_.De(a,c)&&_.De(xn,c)&&yn.test(a[c])&&(b[c]=a[c]);return b};_.g=_.Ln.prototype;_.g.close=function(a,b){return On(this,"_g_close",a,b)};_.g.restyle=function(a,b){return On(this,"_g_restyle",a,b)};_.g.Qr=function(a,b){return On(this,"_g_restyleDone",a,b)};_.g.X7=function(a){return this.getContext().closeSelf(a,void 0,this)};_.g.ifa=function(a){if(a&&typeof a==="object")return this.getContext().restyleSelf(a,void 0,this)};
_.g.jfa=function(a){var b=this.Ka.T.onRestyle;b&&b.call(this,a,this);a=a&&typeof a==="object"?Nn(a):{};(b=this.getIframeEl())&&a&&typeof a==="object"&&(_.De(a,"height")&&(a.height=_.zn(a.height)),_.De(a,"width")&&(a.width=_.zn(a.width)),_.Ee(a,b.style))};
_.g.Y7=function(a){var b=this.Ka.T.onClose;b&&b.call(this,a,this);if(b=Gn(this.getOptions())){var c=this.getContext().getWindow().document.getElementById(this.getId());c&&c.parentNode&&c.parentNode.removeChild(c);c=this.getContext().getWindow();_.Dd&&_.Dh&&c?(c.focus(),Kn(c,b,0)):(b.close(),Jn(b))}b||(b=this.getIframeEl())&&b.parentNode&&b.parentNode.removeChild(b);if(b=this.Ka.Fz())c={},c.frameName=this.getFrameName(),On(b,"_g_disposeControl",c);b=Mn(this,"_g_wasClosed");fn(b,a,this)};
_.g.registerWasRestyled=function(a,b){this.register("_g_wasRestyled",a,b)};_.g.registerWasClosed=function(a,b){this.register("_g_wasClosed",a,b)};_.g.Mha=function(){delete this.getContext().dg[this.getFrameName()];this.getContext().getWindow().setTimeout((0,_.z)(function(){this.dispose()},this),0)};
_.g.send=function(a,b,c,d){_.Kl(!this.isDisposed(),"Cannot send message to disposed iframe - "+a);_.Kl((d||_.cn)(this),"Wrong target for message "+a);c=new pn(c);a=this.qb.getFrameName()+":"+this.uq+":"+a;_.Cl(this.DK,a,c.resolve,b);return c.promise};var On=function(a,b,c,d){return a.send(b,c,d,_.dn)};_.g=_.Ln.prototype;_.g.iea=function(a){return a};_.g.ping=function(a,b){return On(this,"_g_ping",b,a)};
_.g.h8=function(a){a=a&&typeof a==="object"?a:{};for(var b=a.rpcAddr,c=(this.og()+"/"+b).split("/"),d=this.getContext().getWindow(),e;(e=c.shift())&&d;)d=e==".."?d.parent:d.frames[e];_.Kl(!!d,"Bad rpc address "+b);a._window=d;a._parentRpcAddr=this.og();a._parentRetAddr=this.Zh();this.getContext();b=new _.Pn(a);this.wda&&this.wda(b,a.controllerData);this.tF=this.tF||[];this.tF.push(b,a.controllerData)};
_.g.x8=function(a){a=(a||{}).frameName;for(var b=this.tF||[],c=0;c<b.length;c++)if(b[c].getFrameName()===a){a=b.splice(c,1)[0];a.dispose();this.Ada&&this.Ada(a);return}_.Kl(!1,"Unknown contolled iframe to dispose - "+a)};
_.g.e8=function(a){var b=new Cn(a);a=new An(b.value());if(a.T.selfConnect)var c=this;else(_.Kl(mn.test(b.getOrigin()),"Illegal origin for connected iframe - "+b.getOrigin()),c=this.getContext().dg[b.getFrameName()],c)?Um(b)&&(c.Vk(),On(c,"_g_rpcReady")):(b=Dn(Fn(En(new Cn,b.og()),b.Zh()).Mj(b.getOrigin()),b.getFrameName()).Vk(Um(b)).Zm(Fm(b)),c=this.getContext().attach(b.value()));b=this.getContext();var d=a.T.role;a=a.T.data;Qn(b);d=d||"";_.Be(b.rF,d,[]).push({ef:c,data:a});Rn(c,a,b.wJ[d])};
_.g.iM=function(a,b){(new Cn(b)).T._relayedDepth||(b={},Vm(Sm(new An(b),"_opener")),On(a,"_g_connect",b))};
_.g.VX=function(a){var b=this,c=a.T.messageHandlers,d=a.T.messageHandlersFilter,e=a.T.onClose;_.Jm(_.ek(_.dk(a,null),null),null);return On(this,"_g_open",a.value()).then(function(f){var h=new Cn(f[0]),k=h.getFrameName();f=new Cn;var l=b.Zh(),m=h.Zh();Fn(En(f,b.og()+"/"+h.og()),m+"/"+l);Dn(f,k);f.Mj(h.getOrigin());f.Wr(Im(h));f.Zm(Fm(a));_.dk(f,c);_.ek(f,d);_.Jm(f,e);(h=b.getContext().dg[k])||(h=b.getContext().attach(f.value()));return h})};
_.g.GK=function(a){var b=a.getUrl();_.Kl(!b||_.am.test(b),"Illegal url for new iframe - "+b);var c=a.Vn().value();b={};for(var d in c)_.De(c,d)&&_.De(nn,d)&&(b[d]=c[d]);_.De(c,"style")&&(d=c.style,typeof d==="object"&&(b.style=Nn(d)));a.value().attributes=b};
_.g.Sda=function(a){a=new Cn(a);this.GK(a);var b=a.T._relayedDepth||0;a.T._relayedDepth=b+1;a.T.openerIframe=this;var c=Fm(a);a.Zm(null);var d=this;return this.getContext().open(a.value()).then(function(e){var f=Im(new Cn(e.wc())),h=new Cn;In(e,d,h);b==0&&Sm(new An(h.value()),"_opener");h.Vk(!0);h.Zm(c);On(e,"_g_connect",h.value());h=new Cn;Dn(Fn(En(h,e.og()),e.DZ),e.getFrameName()).Mj(e.getOrigin()).Wr(f);return h.value()})};
_.g.hfa=function(a){this.getContext().addOnOpenerHandler(function(b){b.send("_g_wasRestyled",a,void 0,_.dn)},null,_.dn)};var Wn;_.Sn=_.Ce();_.Tn=_.Ce();_.Un=function(a,b){_.Sn[a]=b};_.Vn=function(a){return _.Sn[a]};Wn=function(a,b){_.Fe.load("gapi.iframes.style."+a,b)};_.Xn=function(a,b){_.Tn[a]=b};_.Yn=function(a){return _.Tn[a]};_.Pn=function(a){a=a||{};this.Lg=!1;this.wi=_.Ce();this.dg=_.Ce();this.Vf=a._window||_.xe;this.Hd=this.Vf.location.href;this.mY=(this.QJ=Zn(this.Hd,"parent"))?Zn(this.Hd,"pfname"):"";this.Da=this.QJ?Zn(this.Hd,"_gfid")||Zn(this.Hd,"id"):"";this.uq=_.nm(this.Da,this.mY);this.Cd=_.Ig(this.Hd);if(this.Da){var b=new Cn;En(b,a._parentRpcAddr||"..");Fn(b,a._parentRetAddr||this.Da);b.Mj(_.Ig(this.QJ||this.Hd));Dn(b,this.mY);this.Gb=this.attach(b.value())}else this.Gb=null};_.g=_.Pn.prototype;
_.g.isDisposed=function(){return this.Lg};_.g.dispose=function(){if(!this.isDisposed()){for(var a=_.Aa(Object.values(this.dg)),b=a.next();!b.done;b=a.next())b.value.dispose();this.Lg=!0}};_.g.getFrameName=function(){return this.uq};_.g.getOrigin=function(){return this.Cd};_.g.getWindow=function(){return this.Vf};_.g.ub=function(){return this.Vf.document};_.g.setGlobalParam=function(a,b){this.wi[a]=b};_.g.getGlobalParam=function(a){return this.wi[a]};
_.g.attach=function(a){_.Kl(!this.isDisposed(),"Cannot attach iframe in disposed context");a=new Cn(a);a.og()||En(a,a.getId());a.Zh()||Fn(a,"..");a.getOrigin()||a.Mj(_.Ig(a.getUrl()));a.getFrameName()||Dn(a,_.nm(a.getId(),this.uq));var b=a.getFrameName();if(this.dg[b])return this.dg[b];var c=a.og(),d=c;a.getOrigin()&&(d=c+"|"+a.getOrigin());var e=a.Zh(),f=Fm(a);f||(f=(f=a.getIframeEl())&&(f.getAttribute("data-postorigin")||f.src)||a.getUrl(),f=_.Ge(f,"rpctoken"));Hn(a,_.Jl(d,e,f,Gn(a)));d=((window.gadgets||
{}).rpc||{}).setAuthToken;f&&d&&d(c,f);var h=new _.Ln(this,c,b,a),k=a.T.messageHandlersFilter;_.Qm(a.T.messageHandlers,function(l,m){h.register(m,l,k)});Um(a)&&h.Vk();On(h,"_g_rpcReady");return h};_.g.GK=function(a){Dn(a,null);var b=a.getId();!b||bn.test(b)&&!this.getWindow().document.getElementById(b)||(_.Vf.log("Ignoring requested iframe ID - "+b),a.Me(null))};var Zn=function(a,b){var c=_.Ge(a,b);c||(c=_.Qf(_.Ge(a,"jcp",""))[b]);return c||""};
_.Pn.prototype.openChild=function(a){_.Kl(!this.isDisposed(),"Cannot open iframe in disposed context");var b=new Cn(a);$n(this,b);var c=b.getFrameName();if(c&&this.dg[c])return this.dg[c];this.GK(b);c=b.getUrl();_.Kl(c,"No url for new iframe");var d=b.T.queryParams||{};d.usegapi="1";_.Gm(b,d);d=this.GU&&this.GU(c,b);d||(d=b.T.where,_.Kl(!!d,"No location for new iframe"),c=_.xm(c,d,a),b.T.iframeEl=c,d=c.getAttribute("id"));En(b,d).Me(d);b.Mj(_.Ig(b.T.eurl||""));this.SW&&this.SW(b,b.getIframeEl());
c=this.attach(a);c.iM&&c.iM(c,a);(a=b.T.onCreate)&&a(c);b.T.disableRelayOpen||c.applyIframesApi("_open");return c};
var ao=function(a,b,c){var d=b.T.canvasUrl;if(!d)return c;_.Kl(!b.T.allowPost&&!b.T.forcePost,"Post is not supported when using canvas url");var e=b.getUrl();_.Kl(e&&_.Ig(e)===a.Cd&&_.Ig(d)===a.Cd,"Wrong origin for canvas or hidden url "+d);b.setUrl(d);_.Lm(b);b.T.canvasUrl=null;return function(f){var h=f.getWindow(),k=h.location.hash;k=_.wm(e)+(/#/.test(e)?k.replace(/^#/,"&"):k);h.location.replace(k);c&&c(f)}},bo=function(a,b,c){var d=b.T.relayOpen;if(d){var e=a.getParentIframe();d instanceof _.Ln?
(e=d,_.Hm(b,0)):Number(d)>0&&_.Hm(b,Number(d)-1);if(e){_.Kl(!!e.VX,"Relaying iframe open is disabled");if(d=b.getStyle())if(d=_.Tn[d])b.wp(a),d(b.value()),b.wp(null);b.T.openerIframe=null;c.resolve(e.VX(b));return!0}}return!1},co=function(a,b,c){var d=b.getStyle();if(d)if(_.Kl(!!_.Vn,"Defer style is disabled, when requesting style "+d),_.Sn[d])$n(a,b);else return Wn(d,function(){_.Kl(!!_.Sn[d],"Fail to load style - "+d);c.resolve(a.open(b.value()))}),!0;return!1};
_.Pn.prototype.open=function(a,b){_.Kl(!this.isDisposed(),"Cannot open iframe in disposed context");var c=new Cn(a);b=ao(this,c,b);var d=new pn(b);(b=c.getUrl())&&c.setUrl(_.wm(b));if(bo(this,c,d)||co(this,c,d)||bo(this,c,d))return d.promise;if(Mm(c)!=null){var e=setTimeout(function(){h.getIframeEl().src="about:blank";d.reject({timeout:"Exceeded time limit of :"+Mm(c)+"milliseconds"})},Mm(c)),f=d.resolve;d.resolve=function(k){clearTimeout(e);f(k)}}c.T.waitForOnload&&Em(c.Vn(),function(){d.resolve(h)});
var h=this.openChild(a);c.T.waitForOnload||d.resolve(h);return d.promise};_.Pn.prototype.getParentIframe=function(){return this.Gb};var eo=function(a,b){var c=a.getParentIframe(),d=!0;b.filter&&(d=b.filter.call(b.ef,b.params));return _.Bk(d).then(function(e){return e&&c?(b.kY&&b.kY.call(a,b.params),e=b.sender?b.sender(b.params):On(c,b.message,b.params),b.Kha?e.then(function(){return!0}):!0):!1})};_.g=_.Pn.prototype;
_.g.closeSelf=function(a,b,c){a=eo(this,{sender:function(d){var e=_.an.getParentIframe();_.Qm(_.an.dg,function(f){f!==e&&On(f,"_g_wasClosed",d)});return On(e,"_g_closeMe",d)},message:"_g_closeMe",params:a,ef:c,filter:this.getGlobalParam("onCloseSelfFilter")});b=new pn(b);b.resolve(a);return b.promise};_.g.restyleSelf=function(a,b,c){a=a||{};b=new pn(b);b.resolve(eo(this,{message:"_g_restyleMe",params:a,ef:c,filter:this.getGlobalParam("onRestyleSelfFilter"),Kha:!0,kY:this.B1}));return b.promise};
_.g.B1=function(a){a.height==="auto"&&(a.height=_.zm())};_.g.setCloseSelfFilter=function(a){this.setGlobalParam("onCloseSelfFilter",a)};_.g.setRestyleSelfFilter=function(a){this.setGlobalParam("onRestyleSelfFilter",a)};var $n=function(a,b){var c=b.getStyle();if(c){b.Ei(null);var d=_.Sn[c];_.Kl(d,"No such style: "+c);b.wp(a);d(b.value());b.wp(null)}};
_.Pn.prototype.ready=function(a,b,c,d){var e=b||{},f=this.getParentIframe();this.addOnOpenerHandler(function(k){_.Qm(e,function(l,m){k.register(m,l,d)},this);k!==f&&k.send("_ready",h,void 0,d)},void 0,d);var h=a||{};h.height=h.height||"auto";this.B1(h);f&&f.send("_ready",h,c,_.dn)};
_.Pn.prototype.connectIframes=function(a,b){a=new An(a);var c=new An(b),d=Um(a);b=a.getIframe();var e=c.getIframe();if(e){var f=Fm(a),h=new Cn;In(b,e,h);Tm(Sm((new An(h.value())).Zm(f),a.T.role),a.T.data).Vk(d);var k=new Cn;In(e,b,k);Tm(Sm((new An(k.value())).Zm(f),c.T.role),c.T.data).Vk(!0);On(b,"_g_connect",h.value(),function(){d||On(e,"_g_connect",k.value())});d&&On(e,"_g_connect",k.value())}else c={},Tm(Sm(Vm(new An(c)),a.T.role),a.T.data),On(b,"_g_connect",c)};
var Qn=function(a){a.rF||(a.rF=_.Ce(),a.wJ=_.Ce())};_.Pn.prototype.addOnConnectHandler=function(a,b,c,d){Qn(this);typeof a==="object"?(b=new Wm(a),c=b.QT()||""):(b=Ym(Xm(a).Fc(b).Wr(c),d),c=a);d=this.rF[c]||[];a=!1;for(var e=0;e<d.length&&!a;e++)Rn(this.dg[d[e].ef.getFrameName()],d[e].data,[b]),a=b.T.runOnce;c=_.Be(this.wJ,c,[]);a||b.T.dontWait||c.push(b)};
_.Pn.prototype.removeOnConnectHandler=function(a,b){a=_.Be(this.wJ,a,[]);if(b)for(var c=!1,d=0;!c&&d<a.length;d++)a[d].wb()===b&&(c=!0,a.splice(d,1));else a.splice(0,a.length)};var Rn=function(a,b,c){c=c||[];for(var d=0;d<c.length;d++){var e=c[d];if(e&&a){var f=e.T.filter||_.cn;if(a&&f(a)){f=Im(e)||[];for(var h=0;h<f.length;h++)a.applyIframesApi(f[h]);e.wb()&&e.wb()(a,b);e.T.runOnce&&(c.splice(d,1),--d)}}}};
_.Pn.prototype.addOnOpenerHandler=function(a,b,c){var d=this.addOnConnectHandler;a=Ym(Xm("_opener").Fc(a).Wr(b),c);a.T.runOnce=!0;d.call(this,a.value())};_.Pn.prototype.SW=function(a,b){var c=a.Fz();if(c){_.Kl(c.Cd===a.getOrigin(),"Wrong controller origin "+this.Cd+" !== "+a.getOrigin());var d=a.og();En(a,c.og());Fn(a,c.Zh());var e=new Cn;Km(En(e,d),a.T.controllerData);_.Le(b,"load",function(){c.send("_g_control",e.value())})}};
var fo=function(a,b,c){a=a.getWindow();var d=a.document,e=c.T.reuseWindow;if(e){var f=c.getId();if(!f)throw Error("G");}else f=_.mm(d,c);var h=f,k=c.T.rpcRelayUrl;if(k){k=_.vm(k);h=c.T.fragmentParams||{};h.rly=f;c.T.fragmentParams=h;h=c.T.where||d.body;_.Kl(!!h,"Cannot open window in a page with no body");var l={};l.src=k;l.style="display:none;";l.id=f;l.name=f;_.qm(d,h,l,f);h=f+"_relay"}b=_.wm(b);var m=_.om(d,b,f,c.value());c.T.eurl=m;b=c.T.openAsWindow;typeof b!=="string"&&(b=void 0);c=window.navigator.userAgent||
"";/Trident|MSIE/i.test(c)&&/#/.test(c)&&(m="javascript:window.location.replace("+_.xe.JSON.stringify(m).replace(/#/g,"\\x23")+")");if(e){var n=e;setTimeout(function(){n.location.replace(m)})}else n=_.Fc(a,m,h,b);return{id:f,c2:n}};_.Pn.prototype.GU=function(a,b){if(b.T.openAsWindow){a=fo(this,a,b);var c=a.id;_.Kl(!!a.c2,"Open popup window failed");b.T._popupWindow=a.c2}return c};Zm=_.Ce();$m=_.Ce();_.an=new _.Pn;kn("_g_rpcReady",_.Ln.prototype.Vk);kn("_g_discover",_.Ln.prototype.B$);kn("_g_ping",_.Ln.prototype.iea);kn("_g_close",_.Ln.prototype.X7);kn("_g_closeMe",_.Ln.prototype.Y7);kn("_g_restyle",_.Ln.prototype.ifa);kn("_g_restyleMe",_.Ln.prototype.jfa);kn("_g_wasClosed",_.Ln.prototype.Mha);_.jn("control","_g_control",_.Ln.prototype.h8);_.jn("control","_g_disposeControl",_.Ln.prototype.x8);var go=_.an.getParentIframe();
go&&go.register("_g_restyleDone",_.Ln.prototype.hfa,_.dn);kn("_g_connect",_.Ln.prototype.e8);var ho={};ho._g_open=_.Ln.prototype.Sda;_.hn("_open",ho,_.dn);var io={Context:_.Pn,Iframe:_.Ln,SAME_ORIGIN_IFRAMES_FILTER:_.cn,CROSS_ORIGIN_IFRAMES_FILTER:_.dn,makeWhiteListIframesFilter:_.en,getContext:_.ln,registerIframesApi:_.hn,registerIframesApiHandler:_.jn,registerStyle:_.Un,registerBeforeOpenStyle:_.Xn,getStyle:_.Vn,getBeforeOpenStyle:_.Yn,create:_.xm};vn({instance:function(){return io},priority:2});_.jn("gapi.load","_g_gapi.load",function(a){return new _.xk(function(b){_.Fe.load(a&&typeof a==="object"&&a.features||"",b)})});
_.jo=function(a){this.T=a};_.g=_.jo.prototype;_.g.VK=function(a){this.T.anchor=a;return this};_.g.aj=function(){return this.T.anchor};_.g.WK=function(a){this.T.anchorPosition=a};_.g.Td=function(a){this.T.height=a;return this};_.g.Nc=function(){return this.T.height};_.g.Ne=function(a){this.T.width=a;return this};_.g.Qb=function(){return this.T.width};_.g.setZIndex=function(a){this.T.zIndex=a;return this};_.g.getZIndex=function(){return this.T.zIndex};
_.ko=function(a){a.T.connectWithQueryParams=!0;return a};
_.t("gapi.iframes.create",_.xm);
_.t("gapi.iframes.registerStyle",_.Un);_.t("gapi.iframes.registerBeforeOpenStyle",_.Xn);_.t("gapi.iframes.getStyle",_.Vn);_.t("gapi.iframes.getBeforeOpenStyle",_.Yn);_.t("gapi.iframes.registerIframesApi",_.hn);_.t("gapi.iframes.registerIframesApiHandler",_.jn);_.t("gapi.iframes.getContext",_.ln);_.t("gapi.iframes.SAME_ORIGIN_IFRAMES_FILTER",_.cn);_.t("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.dn);_.t("gapi.iframes.makeWhiteListIframesFilter",_.en);_.t("gapi.iframes.Context",_.Pn);
_.t("gapi.iframes.Context.prototype.isDisposed",_.Pn.prototype.isDisposed);_.t("gapi.iframes.Context.prototype.getWindow",_.Pn.prototype.getWindow);_.t("gapi.iframes.Context.prototype.getFrameName",_.Pn.prototype.getFrameName);_.t("gapi.iframes.Context.prototype.getGlobalParam",_.Pn.prototype.getGlobalParam);_.t("gapi.iframes.Context.prototype.setGlobalParam",_.Pn.prototype.setGlobalParam);_.t("gapi.iframes.Context.prototype.open",_.Pn.prototype.open);
_.t("gapi.iframes.Context.prototype.openChild",_.Pn.prototype.openChild);_.t("gapi.iframes.Context.prototype.getParentIframe",_.Pn.prototype.getParentIframe);_.t("gapi.iframes.Context.prototype.closeSelf",_.Pn.prototype.closeSelf);_.t("gapi.iframes.Context.prototype.restyleSelf",_.Pn.prototype.restyleSelf);_.t("gapi.iframes.Context.prototype.setCloseSelfFilter",_.Pn.prototype.setCloseSelfFilter);_.t("gapi.iframes.Context.prototype.setRestyleSelfFilter",_.Pn.prototype.setRestyleSelfFilter);
_.t("gapi.iframes.Context.prototype.addOnConnectHandler",_.Pn.prototype.addOnConnectHandler);_.t("gapi.iframes.Context.prototype.removeOnConnectHandler",_.Pn.prototype.removeOnConnectHandler);_.t("gapi.iframes.Context.prototype.addOnOpenerHandler",_.Pn.prototype.addOnOpenerHandler);_.t("gapi.iframes.Context.prototype.connectIframes",_.Pn.prototype.connectIframes);_.t("gapi.iframes.Iframe",_.Ln);_.t("gapi.iframes.Iframe.prototype.isDisposed",_.Ln.prototype.isDisposed);
_.t("gapi.iframes.Iframe.prototype.getContext",_.Ln.prototype.getContext);_.t("gapi.iframes.Iframe.prototype.getFrameName",_.Ln.prototype.getFrameName);_.t("gapi.iframes.Iframe.prototype.getId",_.Ln.prototype.getId);_.t("gapi.iframes.Iframe.prototype.register",_.Ln.prototype.register);_.t("gapi.iframes.Iframe.prototype.unregister",_.Ln.prototype.unregister);_.t("gapi.iframes.Iframe.prototype.send",_.Ln.prototype.send);_.t("gapi.iframes.Iframe.prototype.applyIframesApi",_.Ln.prototype.applyIframesApi);
_.t("gapi.iframes.Iframe.prototype.getIframeEl",_.Ln.prototype.getIframeEl);_.t("gapi.iframes.Iframe.prototype.getSiteEl",_.Ln.prototype.getSiteEl);_.t("gapi.iframes.Iframe.prototype.setSiteEl",_.Ln.prototype.setSiteEl);_.t("gapi.iframes.Iframe.prototype.getWindow",_.Ln.prototype.getWindow);_.t("gapi.iframes.Iframe.prototype.getOrigin",_.Ln.prototype.getOrigin);_.t("gapi.iframes.Iframe.prototype.close",_.Ln.prototype.close);_.t("gapi.iframes.Iframe.prototype.restyle",_.Ln.prototype.restyle);
_.t("gapi.iframes.Iframe.prototype.restyleDone",_.Ln.prototype.Qr);_.t("gapi.iframes.Iframe.prototype.registerWasRestyled",_.Ln.prototype.registerWasRestyled);_.t("gapi.iframes.Iframe.prototype.registerWasClosed",_.Ln.prototype.registerWasClosed);_.t("gapi.iframes.Iframe.prototype.getParam",_.Ln.prototype.getParam);_.t("gapi.iframes.Iframe.prototype.setParam",_.Ln.prototype.setParam);_.t("gapi.iframes.Iframe.prototype.ping",_.Ln.prototype.ping);_.t("gapi.iframes.Iframe.prototype.getOpenParams",_.Ln.prototype.wc);
_.af=_.af||{};
_.af=_.af||{};
(function(){function a(c){var d=typeof c==="undefined";if(b!==null&&d)return b;var e={};c=c||window.location.href;var f=c.indexOf("?"),h=c.indexOf("#");c=(h===-1?c.substr(f+1):[c.substr(f+1,h-f-1),"&",c.substr(h+1)].join("")).split("&");f=window.decodeURIComponent?decodeURIComponent:unescape;h=0;for(var k=c.length;h<k;++h){var l=c[h].indexOf("=");if(l!==-1){var m=c[h].substring(0,l);l=c[h].substring(l+1);l=l.replace(/\+/g," ");try{e[m]=f(l)}catch(n){}}}d&&(b=e);return e}var b=null;_.af.Rg=a;a()})();_.t("gadgets.util.getUrlParameters",_.af.Rg);
_.Lg=window.googleapis&&window.googleapis.server||{};
_.ef=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.Ye(a());return{register:function(b,c,d){d&&d(_.Xe())},get:function(b){return _.Xe(b)},update:function(b,c){if(c)throw"Config replacement is not supported";_.Ye(b)},init:function(){}}}();_.t("gadgets.config.register",_.ef.register);_.t("gadgets.config.get",_.ef.get);_.t("gadgets.config.init",_.ef.init);_.t("gadgets.config.update",_.ef.update);
_.t("gadgets.json.stringify",_.Rf);_.t("gadgets.json.parse",_.Qf);
(function(){function a(e,f){if(!(e<c)&&d)if(e===2&&d.warn)d.warn(f);else if(e===3&&d.error)try{d.error(f)}catch(h){}else d.log&&d.log(f)}var b=function(e){a(1,e)};_.bf=function(e){a(2,e)};_.cf=function(e){a(3,e)};_.df=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();
_.af=_.af||{};(function(){var a=[];_.af.rsa=function(b){a.push(b)};_.af.Fsa=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
_.Wf=function(){var a=_.ye.readyState;return a==="complete"||a==="interactive"&&navigator.userAgent.indexOf("MSIE")==-1};_.Xf=function(a){if(_.Wf())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.xe.addEventListener?(_.xe.addEventListener("load",c,!1),_.xe.addEventListener("DOMContentLoaded",c,!1)):_.xe.attachEvent&&(_.xe.attachEvent("onreadystatechange",function(){_.Wf()&&c.apply(this,arguments)}),_.xe.attachEvent("onload",c))}};
_.Yf=function(a,b){var c=_.Be(_.Me,"watt",_.Ce());_.Be(c,a,b)};_.Ge(_.xe.location.href,"rpctoken")&&_.Le(_.ye,"unload",function(){});var Zf=Zf||{};Zf.HZ=null;Zf.rX=null;Zf.FA=null;Zf.frameElement=null;Zf=Zf||{};
Zf.TN||(Zf.TN=function(){function a(f,h,k){typeof window.addEventListener!="undefined"?window.addEventListener(f,h,k):typeof window.attachEvent!="undefined"&&window.attachEvent("on"+f,h);f==="message"&&(window.___jsl=window.___jsl||{},f=window.___jsl,f.RPMQ=f.RPMQ||[],f.RPMQ.push(h))}function b(f){var h=_.Qf(f.data);if(h&&h.f){_.df();var k=_.$f.co(h.f);e&&(typeof f.origin!=="undefined"?f.origin!==k:f.domain!==/^.+:\/\/([^:]+).*/.exec(k)[1])?_.cf("Invalid rpc message origin. "+k+" vs "+(f.origin||"")):
c(h,f.origin)}}var c,d,e=!0;return{nT:function(){return"wpm"},nca:function(){return!0},init:function(f,h){_.ef.register("rpc",null,function(k){String((k&&k.rpc||{}).disableForceSecure)==="true"&&(e=!1)});c=f;d=h;a("message",b,!1);d("..",!0);return!0},Ib:function(f){d(f,!0);return!0},call:function(f,h,k){var l=_.$f.co(f),m=_.$f.MO(f);l?window.setTimeout(function(){var n=_.Rf(k);_.df();m&&"postMessage"in m&&m.postMessage(n,l)},0):f!=".."&&_.cf("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message");
return!0}}}());if(window.gadgets&&window.gadgets.rpc)typeof _.$f!="undefined"&&_.$f||(_.$f=window.gadgets.rpc,_.$f.config=_.$f.config,_.$f.register=_.$f.register,_.$f.unregister=_.$f.unregister,_.$f.jZ=_.$f.registerDefault,_.$f.A1=_.$f.unregisterDefault,_.$f.SS=_.$f.forceParentVerifiable,_.$f.call=_.$f.call,_.$f.Gu=_.$f.getRelayUrl,_.$f.Oj=_.$f.setRelayUrl,_.$f.OC=_.$f.setAuthToken,_.$f.Iw=_.$f.setupReceiver,_.$f.Pn=_.$f.getAuthToken,_.$f.vK=_.$f.removeReceiver,_.$f.NT=_.$f.getRelayChannel,_.$f.eZ=_.$f.receive,
_.$f.fZ=_.$f.receiveSameDomain,_.$f.getOrigin=_.$f.getOrigin,_.$f.co=_.$f.getTargetOrigin,_.$f.MO=_.$f._getTargetWin,_.$f.N6=_.$f._parseSiblingId);else{_.$f=function(){function a(I,ka){if(!T[I]){var ma=cb;ka||(ma=Oa);T[I]=ma;ka=K[I]||[];for(var Fa=0;Fa<ka.length;++Fa){var U=ka[Fa];U.t=E[I];ma.call(I,U.f,U)}K[I]=[]}}function b(){function I(){Mb=!0}Hb||(typeof window.addEventListener!="undefined"?window.addEventListener("unload",I,!1):typeof window.attachEvent!="undefined"&&window.attachEvent("onunload",
I),Hb=!0)}function c(I,ka,ma,Fa,U){E[ka]&&E[ka]===ma||(_.cf("Invalid gadgets.rpc token. "+E[ka]+" vs "+ma),qb(ka,2));U.onunload=function(){R[ka]&&!Mb&&(qb(ka,1),_.$f.vK(ka))};b();Fa=_.Qf(decodeURIComponent(Fa))}function d(I,ka){if(I&&typeof I.s==="string"&&typeof I.f==="string"&&I.a instanceof Array)if(E[I.f]&&E[I.f]!==I.t&&(_.cf("Invalid gadgets.rpc token. "+E[I.f]+" vs "+I.t),qb(I.f,2)),I.s==="__ack")window.setTimeout(function(){a(I.f,!0)},0);else{I.c&&(I.callback=function(Ga){_.$f.call(I.f,(I.g?
"legacy__":"")+"__cb",null,I.c,Ga)});if(ka){var ma=e(ka);I.origin=ka;var Fa=I.r;try{var U=e(Fa)}catch(Ga){}Fa&&U==ma||(Fa=ka);I.referer=Fa}ka=(x[I.s]||x[""]).apply(I,I.a);I.c&&typeof ka!=="undefined"&&_.$f.call(I.f,"__cb",null,I.c,ka)}}function e(I){if(!I)return"";I=I.split("#")[0].split("?")[0];I=I.toLowerCase();I.indexOf("//")==0&&(I=window.location.protocol+I);I.indexOf("://")==-1&&(I=window.location.protocol+"//"+I);var ka=I.substring(I.indexOf("://")+3),ma=ka.indexOf("/");ma!=-1&&(ka=ka.substring(0,
ma));I=I.substring(0,I.indexOf("://"));if(I!=="http"&&I!=="https"&&I!=="chrome-extension"&&I!=="file"&&I!=="android-app"&&I!=="chrome-search"&&I!=="chrome-untrusted"&&I!=="chrome"&&I!=="devtools")throw Error("l");ma="";var Fa=ka.indexOf(":");if(Fa!=-1){var U=ka.substring(Fa+1);ka=ka.substring(0,Fa);if(I==="http"&&U!=="80"||I==="https"&&U!=="443")ma=":"+U}return I+"://"+ka+ma}function f(I){if(I.charAt(0)=="/"){var ka=I.indexOf("|"),ma=ka>0?I.substring(1,ka):I.substring(1);I=ka>0?I.substring(ka+1):
null;return{id:ma,origin:I}}return null}function h(I){if(typeof I==="undefined"||I==="..")return window.parent;var ka=f(I);if(ka)return k(window.top.frames[ka.id]);I=String(I);return(ka=window.frames[I])?k(ka):(ka=document.getElementById(I))&&ka.contentWindow?ka.contentWindow:null}function k(I){return I?"postMessage"in I?I:I instanceof HTMLIFrameElement&&"contentWindow"in I&&I.contentWindow!=null&&"postMessage"in I.contentWindow?I.contentWindow:null:null}function l(I,ka){if(R[I]!==!0){typeof R[I]===
"undefined"&&(R[I]=0);var ma=h(I);I!==".."&&ma==null||cb.Ib(I,ka)!==!0?R[I]!==!0&&R[I]++<10?window.setTimeout(function(){l(I,ka)},500):(T[I]=Oa,R[I]=!0):R[I]=!0}}function m(I){(I=A[I])&&I.substring(0,1)==="/"&&(I=I.substring(1,2)==="/"?document.location.protocol+I:document.location.protocol+"//"+document.location.host+I);return I}function n(I,ka,ma){ka&&!/http(s)?:\/\/.+/.test(ka)&&(ka.indexOf("//")==0?ka=window.location.protocol+ka:ka.charAt(0)=="/"?ka=window.location.protocol+"//"+window.location.host+
ka:ka.indexOf("://")==-1&&(ka=window.location.protocol+"//"+ka));A[I]=ka;typeof ma!=="undefined"&&(D[I]=!!ma)}function p(I,ka){ka=ka||"";E[I]=String(ka);l(I,ka)}function q(I){I=(I.passReferrer||"").split(":",2);O=I[0]||"none";Y=I[1]||"origin"}function r(I){String(I.useLegacyProtocol)==="true"&&(cb=Zf.FA||Oa,cb.init(d,a))}function w(I,ka){function ma(Fa){Fa=Fa&&Fa.rpc||{};q(Fa);var U=Fa.parentRelayUrl||"";U=e(aa.parent||ka)+U;n("..",U,String(Fa.useLegacyProtocol)==="true");r(Fa);p("..",I)}!aa.parent&&
ka?ma({}):_.ef.register("rpc",null,ma)}function u(I,ka,ma){if(I==="..")w(ma||aa.rpctoken||aa.ifpctok||"",ka);else a:{var Fa=null;if(I.charAt(0)!="/"){if(!_.af)break a;Fa=document.getElementById(I);if(!Fa)throw Error("m`"+I);}Fa=Fa&&Fa.src;ka=ka||e(Fa);n(I,ka);ka=_.af.Rg(Fa);p(I,ma||ka.rpctoken)}}var x={},A={},D={},E={},N=0,H={},R={},aa={},T={},K={},O=null,Y=null,oa=window.top!==window.self,La=window.name,qb=function(){},fb=window.console,Cb=fb&&fb.log&&function(I){fb.log(I)}||function(){},Oa=function(){function I(ka){return function(){Cb(ka+
": call ignored")}}return{nT:function(){return"noop"},nca:function(){return!0},init:I("init"),Ib:I("setup"),call:I("call")}}();_.af&&(aa=_.af.Rg());var Mb=!1,Hb=!1,cb=function(){if(aa.rpctx=="rmr")return Zf.HZ;var I=typeof window.postMessage==="function"?Zf.TN:typeof window.postMessage==="object"?Zf.TN:window.ActiveXObject?Zf.rX?Zf.rX:Zf.FA:navigator.userAgent.indexOf("WebKit")>0?Zf.HZ:navigator.product==="Gecko"?Zf.frameElement:Zf.FA;I||(I=Oa);return I}();x[""]=function(){Cb("Unknown RPC service: "+
this.s)};x.__cb=function(I,ka){var ma=H[I];ma&&(delete H[I],ma.call(this,ka))};return{config:function(I){typeof I.VZ==="function"&&(qb=I.VZ)},register:function(I,ka){if(I==="__cb"||I==="__ack")throw Error("n");if(I==="")throw Error("o");x[I]=ka},unregister:function(I){if(I==="__cb"||I==="__ack")throw Error("p");if(I==="")throw Error("q");delete x[I]},jZ:function(I){x[""]=I},A1:function(){delete x[""]},SS:function(){},call:function(I,ka,ma,Fa){I=I||"..";var U="..";I===".."?U=La:I.charAt(0)=="/"&&(U=
e(window.location.href),U="/"+La+(U?"|"+U:""));++N;ma&&(H[N]=ma);var Ga={s:ka,f:U,c:ma?N:0,a:Array.prototype.slice.call(arguments,3),t:E[I],l:!!D[I]};a:if(O==="bidir"||O==="c2p"&&I===".."||O==="p2c"&&I!==".."){var Ha=window.location.href;var fa="?";if(Y==="query")fa="#";else if(Y==="hash")break a;fa=Ha.lastIndexOf(fa);fa=fa===-1?Ha.length:fa;Ha=Ha.substring(0,fa)}else Ha=null;Ha&&(Ga.r=Ha);if(I===".."||f(I)!=null||document.getElementById(I))(Ha=T[I])||f(I)===null||(Ha=cb),ka.indexOf("legacy__")===
0&&(Ha=cb,Ga.s=ka.substring(8),Ga.c=Ga.c?Ga.c:N),Ga.g=!0,Ga.r=U,Ha?(D[I]&&(Ha=Zf.FA),Ha.call(I,U,Ga)===!1&&(T[I]=Oa,cb.call(I,U,Ga))):K[I]?K[I].push(Ga):K[I]=[Ga]},Gu:m,Oj:n,OC:p,Iw:u,Pn:function(I){return E[I]},vK:function(I){delete A[I];delete D[I];delete E[I];delete R[I];delete T[I]},NT:function(){return cb.nT()},eZ:function(I,ka){I.length>4?cb.Qpa(I,d):c.apply(null,I.concat(ka))},fZ:function(I){I.a=Array.prototype.slice.call(I.a);window.setTimeout(function(){d(I)},0)},getOrigin:e,co:function(I){var ka=
null,ma=m(I);ma?ka=ma:(ma=f(I))?ka=ma.origin:I==".."?ka=aa.parent:(I=document.getElementById(I))&&I.tagName.toLowerCase()==="iframe"&&(ka=I.src);return e(ka)},init:function(){cb.init(d,a)===!1&&(cb=Oa);oa?u(".."):_.ef.register("rpc",null,function(I){I=I.rpc||{};q(I);r(I)})},MO:h,N6:f,Wha:"__ack",Wma:La||"..",gna:0,fna:1,ena:2}}();_.$f.init()};_.$f.config({VZ:function(a){throw Error("r`"+a);}});_.t("gadgets.rpc.config",_.$f.config);_.t("gadgets.rpc.register",_.$f.register);_.t("gadgets.rpc.unregister",_.$f.unregister);_.t("gadgets.rpc.registerDefault",_.$f.jZ);_.t("gadgets.rpc.unregisterDefault",_.$f.A1);_.t("gadgets.rpc.forceParentVerifiable",_.$f.SS);_.t("gadgets.rpc.call",_.$f.call);_.t("gadgets.rpc.getRelayUrl",_.$f.Gu);_.t("gadgets.rpc.setRelayUrl",_.$f.Oj);_.t("gadgets.rpc.setAuthToken",_.$f.OC);_.t("gadgets.rpc.setupReceiver",_.$f.Iw);_.t("gadgets.rpc.getAuthToken",_.$f.Pn);
_.t("gadgets.rpc.removeReceiver",_.$f.vK);_.t("gadgets.rpc.getRelayChannel",_.$f.NT);_.t("gadgets.rpc.receive",_.$f.eZ);_.t("gadgets.rpc.receiveSameDomain",_.$f.fZ);_.t("gadgets.rpc.getOrigin",_.$f.getOrigin);_.t("gadgets.rpc.getTargetOrigin",_.$f.co);
_.af=_.af||{};_.af.i7=function(a){var b=window;typeof b.addEventListener!="undefined"?b.addEventListener("mousemove",a,!1):typeof b.attachEvent!="undefined"?b.attachEvent("onmousemove",a):_.bf("cannot attachBrowserEvent: mousemove")};_.af.Jea=function(a){var b=window;b.removeEventListener?b.removeEventListener("mousemove",a,!1):b.detachEvent?b.detachEvent("onmousemove",a):_.bf("cannot removeBrowserEvent: mousemove")};
_.Qg=function(){function a(m){var n=new _.Pg;n.ux(m);return n.Si()}var b=window.crypto;if(b&&typeof b.getRandomValues=="function")return function(){var m=new window.Uint32Array(1);b.getRandomValues(m);return Number("0."+m[0])};var c=_.Xe("random/maxObserveMousemove");c==null&&(c=-1);var d=0,e=Math.random(),f=1,h=(screen.width*screen.width+screen.height)*1E6,k=function(m){m=m||window.event;var n=m.screenX+m.clientX<<16;n+=m.screenY+m.clientY;n*=(new Date).getTime()%1E6;f=f*n%h;c>0&&++d==c&&_.af.Jea(k)};
c!=0&&_.af.i7(k);var l=a(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+e);return function(){var m=f;m+=parseInt(l.substr(0,20),16);l=a(l);return m/(h+1.2089258196146292E24)}}();_.t("shindig.random",_.Qg);
var Rg=function(a){return{execute:function(b){var c={method:a.httpMethod||"GET",root:a.root,path:a.url,params:a.urlParams,headers:a.headers,body:a.body},d=window.gapi,e=function(){var f=d.config.get("client/apiKey"),h=d.config.get("client/version");try{var k=d.config.get("googleapis.config/developerKey"),l=d.config.get("client/apiKey",k);d.config.update("client/apiKey",l);d.config.update("client/version","1.0.0-alpha");var m=d.client;m.request.call(m,c).then(b,b)}finally{d.config.update("client/apiKey",
f),d.config.update("client/version",h)}};d.client?e():d.load.call(d,"client",e)}}},Sg=function(a,b){return function(c){var d={};c=c.body;var e=_.Qf(c),f={};if(e&&e.length)for(var h=e.length,k=0;k<h;++k){var l=e[k];f[l.id]=l}h=b.length;for(k=0;k<h;++k)l=b[k].id,d[l]=e&&e.length?f[l]:e;a(d,c)}},Tg=function(a){a.transport={name:"googleapis",execute:function(b,c){for(var d=[],e=b.length,f=0;f<e;++f){var h=b[f],k=h.method,l=String(k).split(".")[0];l=_.Xe("googleapis.config/versions/"+k)||_.Xe("googleapis.config/versions/"+
l)||"v1";d.push({jsonrpc:"2.0",id:h.id,method:k,apiVersion:String(l),params:h.params})}b=Rg({httpMethod:"POST",root:a.transport.root,url:"/rpc?pp=0",headers:{"Content-Type":"application/json"},body:d});b.execute.call(b,Sg(c,d))},root:void 0}},Ug=function(a){var b=this.method,c=this.transport;c.execute.call(c,[{method:b,id:b,params:this.rpc}],function(d){d=d[b];d.error||(d=d.data||d.result);a(d)})},Wg=function(){for(var a=Vg,b=a.split("."),c=function(k){k=k||{};k.groupId=k.groupId||"@self";k.userId=
k.userId||"@viewer";k={method:a,rpc:k||{}};Tg(k);k.execute=Ug;return k},d=_.Xa,e=b.length,f=0;f<e;++f){var h=d[b[f]]||{};f+1==e&&(h=c);d=d[b[f]]=h}if(b.length>1&&b[0]!="googleapis")for(b[0]="googleapis",b[b.length-1]=="delete"&&(b[b.length-1]="remove"),d=_.Xa,e=b.length,f=0;f<e;++f)h=d[b[f]]||{},f+1==e&&(h=c),d=d[b[f]]=h},Vg;for(Vg in _.Xe("googleapis.config/methods"))Wg();_.t("googleapis.newHttpRequest",function(a){return Rg(a)});_.t("googleapis.setUrlParameter",function(a,b){if(a!=="trace")throw Error("u");_.Ye("client/trace",b)});
});
// Google Inc.
