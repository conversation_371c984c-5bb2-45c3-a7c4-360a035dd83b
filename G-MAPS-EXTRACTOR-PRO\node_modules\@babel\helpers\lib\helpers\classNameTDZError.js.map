{"version": 3, "names": ["_classNameTDZError", "name", "ReferenceError"], "sources": ["../../src/helpers/classNameTDZError.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _classNameTDZError(name) {\n  throw new ReferenceError(\n    'Class \"' + name + '\" cannot be referenced in computed property keys.',\n  );\n}\n"], "mappings": ";;;;;;AAEe,SAASA,kBAAkBA,CAACC,IAAI,EAAE;EAC/C,MAAM,IAAIC,cAAc,CACtB,SAAS,GAAGD,IAAI,GAAG,mDACrB,CAAC;AACH", "ignoreList": []}