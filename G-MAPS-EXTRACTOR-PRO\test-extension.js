// Test script for G Maps Extractor Pro
// Run this in browser console on Google Maps to test extraction

(function() {
  'use strict';

  console.log('🧪 Testing G Maps Extractor Pro...');

  // Test 1: Check if extension is loaded
  function testExtensionLoaded() {
    console.log('\n📋 Test 1: Extension Loading');
    
    const hasContentScript = window.GoogleMapsExtractorInitialized;
    const hasInjectedScript = window.GMapsExtractorInjected;
    
    console.log('✓ Content Script Loaded:', hasContentScript ? '✅' : '❌');
    console.log('✓ Injected Script Loaded:', hasInjectedScript ? '✅' : '❌');
    
    if (hasInjectedScript && window.GMapsExtractorAPI) {
      console.log('✓ Injected API Available:', '✅');
      console.log('✓ API Methods:', Object.keys(window.GMapsExtractorAPI));
    } else {
      console.log('✓ Injected API Available:', '❌');
    }
    
    return hasContentScript && hasInjectedScript;
  }

  // Test 2: Test DOM selectors
  function testDOMSelectors() {
    console.log('\n📋 Test 2: DOM Selectors');
    
    const selectors = {
      'Search Input': [
        '#searchboxinput',
        'input[aria-label*="Search"]',
        'input[placeholder*="Search"]'
      ],
      'Business Results': [
        '[data-result-index]',
        '.Nv2PK',
        '.bfdHYd',
        '.lI9IFe',
        'a[href*="/place/"]'
      ],
      'Business Names': [
        '.qBF1Pd',
        '.fontHeadlineSmall',
        'h3',
        '.DUwDvf'
      ],
      'Business Addresses': [
        '.W4Efsd:nth-child(2)',
        '.Io6YTe',
        '.rogA2c',
        '.fontBodyMedium'
      ]
    };

    Object.entries(selectors).forEach(([category, selectorList]) => {
      console.log(`\n🔍 ${category}:`);
      selectorList.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        const status = elements.length > 0 ? '✅' : '❌';
        console.log(`  ${selector}: ${status} (${elements.length} found)`);
        
        if (elements.length > 0 && elements.length <= 3) {
          elements.forEach((el, i) => {
            const text = el.textContent?.trim().substring(0, 50) || 'No text';
            console.log(`    [${i}]: "${text}"`);
          });
        }
      });
    });
  }

  // Test 3: Test extraction methods
  function testExtractionMethods() {
    console.log('\n📋 Test 3: Extraction Methods');
    
    if (!window.GMapsExtractorAPI) {
      console.log('❌ Injected API not available');
      return;
    }

    // Test DOM extraction
    console.log('\n🔍 Testing DOM Extraction:');
    const domBusinesses = window.GMapsExtractorAPI.extractFromDOM();
    console.log(`✓ DOM Extraction: ${domBusinesses.length} businesses found`);
    
    if (domBusinesses.length > 0) {
      console.log('Sample business:', domBusinesses[0]);
    }

    // Test internal state extraction
    console.log('\n🔍 Testing Internal State Extraction:');
    const internalBusinesses = window.GMapsExtractorAPI.extractFromInternalState();
    console.log(`✓ Internal State: ${internalBusinesses ? internalBusinesses.length : 0} businesses found`);
    
    if (internalBusinesses && internalBusinesses.length > 0) {
      console.log('Sample business:', internalBusinesses[0]);
    }

    // Test API responses
    console.log('\n🔍 Testing API Responses:');
    const apiResponses = window.GMapsExtractorAPI.getStoredAPIResponses();
    console.log(`✓ Stored API Responses: ${apiResponses.length} found`);
    
    if (apiResponses.length > 0) {
      console.log('Recent API calls:', apiResponses.slice(-3).map(r => r.url));
    }
  }

  // Test 4: Test current page detection
  function testPageDetection() {
    console.log('\n📋 Test 4: Page Detection');
    
    const url = window.location.href;
    const isGoogleMaps = url.includes('maps.google') || url.includes('google.com/maps');
    const hasSearchResults = document.querySelectorAll('[data-result-index], .Nv2PK, a[href*="/place/"]').length > 0;
    const isBusinessPage = url.includes('/place/');
    
    console.log('✓ Is Google Maps:', isGoogleMaps ? '✅' : '❌');
    console.log('✓ Has Search Results:', hasSearchResults ? '✅' : '❌');
    console.log('✓ Is Business Page:', isBusinessPage ? '✅' : '❌');
    console.log('✓ Current URL:', url);
    
    return isGoogleMaps;
  }

  // Test 5: Test message communication
  function testMessageCommunication() {
    console.log('\n📋 Test 5: Message Communication');
    
    if (!window.GMapsExtractorAPI) {
      console.log('❌ Cannot test - Injected API not available');
      return;
    }

    // Test extraction request
    console.log('🔄 Sending extraction request...');
    
    const messageHandler = (event) => {
      if (event.source !== window) return;
      
      if (event.data.type === 'GMAPS_EXTRACT_RESPONSE') {
        console.log('✅ Received extraction response:', event.data.data.length, 'businesses');
        if (event.data.data.length > 0) {
          console.log('Sample extracted business:', event.data.data[0]);
        }
        window.removeEventListener('message', messageHandler);
      }
    };

    window.addEventListener('message', messageHandler);
    
    window.postMessage({
      type: 'GMAPS_EXTRACT_REQUEST'
    }, '*');

    // Cleanup after 5 seconds
    setTimeout(() => {
      window.removeEventListener('message', messageHandler);
    }, 5000);
  }

  // Test 6: Performance test
  function testPerformance() {
    console.log('\n📋 Test 6: Performance Test');
    
    const startTime = performance.now();
    
    // Test selector performance
    const selectors = [
      '.qBF1Pd',
      '.fontHeadlineSmall',
      'a[href*="/place/"]',
      '.Io6YTe',
      '.W4Efsd'
    ];
    
    selectors.forEach(selector => {
      const selectorStart = performance.now();
      const elements = document.querySelectorAll(selector);
      const selectorTime = performance.now() - selectorStart;
      
      console.log(`✓ ${selector}: ${elements.length} elements in ${selectorTime.toFixed(2)}ms`);
    });
    
    const totalTime = performance.now() - startTime;
    console.log(`✓ Total test time: ${totalTime.toFixed(2)}ms`);
  }

  // Run all tests
  function runAllTests() {
    console.log('🚀 Starting G Maps Extractor Pro Tests...');
    console.log('=' * 50);
    
    const results = {
      extensionLoaded: testExtensionLoaded(),
      pageDetected: testPageDetection()
    };
    
    testDOMSelectors();
    testExtractionMethods();
    testMessageCommunication();
    testPerformance();
    
    console.log('\n📊 Test Summary:');
    console.log('=' * 30);
    console.log('✓ Extension Loaded:', results.extensionLoaded ? '✅' : '❌');
    console.log('✓ Page Detected:', results.pageDetected ? '✅' : '❌');
    
    if (results.extensionLoaded && results.pageDetected) {
      console.log('🎉 Extension appears to be working correctly!');
      console.log('💡 Try extracting some businesses using the extension popup.');
    } else {
      console.log('⚠️  Some issues detected. Check the logs above.');
    }
    
    console.log('\n🔧 Debugging Tips:');
    console.log('- Make sure you\'re on a Google Maps search results page');
    console.log('- Check if the extension is properly loaded in chrome://extensions/');
    console.log('- Look for any console errors');
    console.log('- Try refreshing the page and running the test again');
  }

  // Export test functions to global scope for manual testing
  window.GMapsExtractorTest = {
    runAllTests,
    testExtensionLoaded,
    testDOMSelectors,
    testExtractionMethods,
    testPageDetection,
    testMessageCommunication,
    testPerformance
  };

  // Auto-run tests
  runAllTests();

})();

// Usage:
// 1. Load this script in browser console on Google Maps
// 2. Or run individual tests: GMapsExtractorTest.testDOMSelectors()
// 3. Or run all tests again: GMapsExtractorTest.runAllTests()
