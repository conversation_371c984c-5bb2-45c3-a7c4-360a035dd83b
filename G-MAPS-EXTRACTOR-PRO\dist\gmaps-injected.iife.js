var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
(function() {
  "use strict";
  (function() {
    class GoogleMapsInterceptor {
      constructor() {
        __publicField(this, "apiResponses", []);
        __publicField(this, "maxStoredResponses", 50);
        __publicField(this, "debug", true);
        this.initializeInterception();
        this.setupMessageHandlers();
        this.log("🚀 Google Maps Interceptor initialized");
      }
      /**
       * Initialize network interception
       */
      initializeInterception() {
        this.interceptFetch();
        this.interceptXHR();
        this.interceptWebSocket();
      }
      /**
       * Intercept Fetch API
       */
      interceptFetch() {
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
          const url = args[0];
          const options = args[1] || {};
          try {
            const response = await originalFetch.apply(window, args);
            if (this.isGoogleMapsAPI(url)) {
              this.log(`📡 Intercepted Fetch: ${url}`);
              const clonedResponse = response.clone();
              try {
                const data = await clonedResponse.json();
                this.storeAPIResponse({
                  url,
                  data,
                  timestamp: Date.now(),
                  method: options.method || "GET"
                });
              } catch (error) {
                try {
                  const text = await clonedResponse.text();
                  if (text.length > 0) {
                    this.storeAPIResponse({
                      url,
                      data: text,
                      timestamp: Date.now(),
                      method: options.method || "GET"
                    });
                  }
                } catch (textError) {
                  this.log(`Error reading response: ${textError.message}`);
                }
              }
            }
            return response;
          } catch (error) {
            this.log(`Fetch error: ${error.message}`);
            throw error;
          }
        };
      }
      /**
       * Intercept XMLHttpRequest
       */
      interceptXHR() {
        const originalOpen = XMLHttpRequest.prototype.open;
        const originalSend = XMLHttpRequest.prototype.send;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
          this._interceptedUrl = url;
          this._interceptedMethod = method;
          return originalOpen.apply(this, [method, url, ...args]);
        };
        XMLHttpRequest.prototype.send = (function(data) {
          const url = this._interceptedUrl;
          const method = this._interceptedMethod;
          if (url && this.isGoogleMapsAPI(url)) {
            this.addEventListener("load", () => {
              if (this.status >= 200 && this.status < 300) {
                try {
                  const responseData = JSON.parse(this.responseText);
                  this.storeAPIResponse({
                    url,
                    data: responseData,
                    timestamp: Date.now(),
                    method
                  });
                  this.log(`📡 Intercepted XHR: ${url}`);
                } catch (error) {
                  if (this.responseText.length > 100) {
                    this.storeAPIResponse({
                      url,
                      data: this.responseText,
                      timestamp: Date.now(),
                      method
                    });
                  }
                }
              }
            });
          }
          return originalSend.apply(this, [data]);
        }).bind(this);
      }
      /**
       * Intercept WebSocket (for real-time updates)
       */
      interceptWebSocket() {
        window.WebSocket = class extends WebSocket {
          constructor(url, protocols) {
            super(url, protocols);
            if (this.isGoogleMapsWebSocket(url.toString())) {
              this.addEventListener("message", (event) => {
                try {
                  const data = JSON.parse(event.data);
                  this.storeAPIResponse({
                    url: url.toString(),
                    data,
                    timestamp: Date.now(),
                    method: "WEBSOCKET"
                  });
                  this.log(`📡 Intercepted WebSocket: ${url}`);
                } catch (error) {
                }
              });
            }
          }
        };
      }
      /**
       * Check if URL is a Google Maps API endpoint
       */
      isGoogleMapsAPI(url) {
        const googleMapsPatterns = [
          "/maps/api/",
          "/search",
          "/place/",
          "maps.googleapis.com",
          "maps.google.com/maps/api",
          "clients1.google.com/complete/search",
          "www.google.com/search",
          "www.google.com/maps/preview/place",
          "www.google.com/maps/rpc/",
          "maps.google.com/maps/rpc/",
          "maps.google.com/maps/vt",
          "khms0.googleapis.com",
          "khms1.googleapis.com"
        ];
        return googleMapsPatterns.some((pattern) => url.includes(pattern));
      }
      /**
       * Check if WebSocket URL is Google Maps related
       */
      isGoogleMapsWebSocket(url) {
        return url.includes("maps.google") || url.includes("googleapis.com");
      }
      /**
       * Store API response
       */
      storeAPIResponse(response) {
        this.apiResponses.push(response);
        if (this.apiResponses.length > this.maxStoredResponses) {
          this.apiResponses = this.apiResponses.slice(-this.maxStoredResponses);
        }
      }
      /**
       * Setup message handlers for communication with content script
       */
      setupMessageHandlers() {
        window.addEventListener("message", (event) => {
          if (event.source !== window) return;
          switch (event.data.type) {
            case "GMAPS_GET_API_RESPONSES":
              this.sendAPIResponses();
              break;
            case "GMAPS_INTERNAL_REQUEST":
              this.extractInternalData();
              break;
            case "GMAPS_CLEAR_CACHE":
              this.clearCache();
              break;
          }
        });
      }
      /**
       * Send stored API responses to content script
       */
      sendAPIResponses() {
        window.postMessage({
          type: "GMAPS_API_RESPONSES",
          responses: this.apiResponses
        }, "*");
        this.log(`📤 Sent ${this.apiResponses.length} API responses`);
      }
      /**
       * Extract data from Google Maps internal state
       */
      extractInternalData() {
        const businesses = [];
        try {
          const internalSources = [
            () => this.extractFromAppState(),
            () => this.extractFromPageData(),
            () => this.extractFromGoogleObjects(),
            () => this.extractFromReactState(),
            () => this.extractFromAngularScope()
          ];
          for (const extractor of internalSources) {
            try {
              const extracted = extractor();
              if (extracted && extracted.length > 0) {
                businesses.push(...extracted);
                this.log(`✅ Extracted ${extracted.length} businesses from internal state`);
              }
            } catch (error) {
              this.log(`Internal extraction method failed: ${error.message}`);
            }
          }
        } catch (error) {
          this.log(`Error extracting internal data: ${error.message}`);
        }
        window.postMessage({
          type: "GMAPS_INTERNAL_RESPONSE",
          businesses: this.deduplicateBusinesses(businesses)
        }, "*");
      }
      /**
       * Extract from APP_INITIALIZATION_STATE
       */
      extractFromAppState() {
        const businesses = [];
        try {
          const appState = window.APP_INITIALIZATION_STATE;
          if (appState && Array.isArray(appState)) {
            this.searchNestedArrays(appState, businesses);
          }
        } catch (error) {
          this.log(`Error extracting from app state: ${error.message}`);
        }
        return businesses;
      }
      /**
       * Extract from _pageData
       */
      extractFromPageData() {
        const businesses = [];
        try {
          const pageData = window._pageData;
          if (pageData) {
            this.searchObjectForBusinessData(pageData, businesses);
          }
        } catch (error) {
          this.log(`Error extracting from page data: ${error.message}`);
        }
        return businesses;
      }
      /**
       * Extract from Google-specific objects
       */
      extractFromGoogleObjects() {
        const businesses = [];
        try {
          const googleObjects = [
            window.google,
            window.gm_authuser_data,
            window.GM_STATE,
            window.GOOGLE_MAPS_DATA
          ];
          for (const obj of googleObjects) {
            if (obj) {
              this.searchObjectForBusinessData(obj, businesses);
            }
          }
        } catch (error) {
          this.log(`Error extracting from Google objects: ${error.message}`);
        }
        return businesses;
      }
      /**
       * Extract from React component state (if Google Maps uses React)
       */
      extractFromReactState() {
        const businesses = [];
        try {
          const reactElements = document.querySelectorAll("[data-reactroot], [data-react-helmet]");
          for (const element of reactElements) {
            const reactInstance = element._reactInternalInstance || element.__reactInternalInstance;
            if (reactInstance) {
              this.searchReactState(reactInstance, businesses);
            }
          }
        } catch (error) {
          this.log(`Error extracting from React state: ${error.message}`);
        }
        return businesses;
      }
      /**
       * Extract from Angular scope (if Google Maps uses Angular)
       */
      extractFromAngularScope() {
        const businesses = [];
        try {
          const angular = window.angular;
          if (angular) {
            const elements = document.querySelectorAll("[ng-app], [data-ng-app], .ng-scope");
            for (const element of elements) {
              const scope = angular.element(element).scope();
              if (scope) {
                this.searchObjectForBusinessData(scope, businesses);
              }
            }
          }
        } catch (error) {
          this.log(`Error extracting from Angular scope: ${error.message}`);
        }
        return businesses;
      }
      /**
       * Search nested arrays for business-like data
       */
      searchNestedArrays(arr, businesses, depth = 0) {
        if (depth > 10 || !Array.isArray(arr)) return;
        for (const item of arr) {
          if (Array.isArray(item)) {
            this.searchNestedArrays(item, businesses, depth + 1);
          } else if (typeof item === "object" && item !== null) {
            if (this.isBusinessLikeObject(item)) {
              const business = this.convertToBusinessData(item);
              if (business) businesses.push(business);
            } else {
              this.searchObjectForBusinessData(item, businesses, depth + 1);
            }
          }
        }
      }
      /**
       * Search object for business data
       */
      searchObjectForBusinessData(obj, businesses, depth = 0) {
        if (depth > 10 || typeof obj !== "object" || obj === null) return;
        if (this.isBusinessLikeObject(obj)) {
          const business = this.convertToBusinessData(obj);
          if (business) businesses.push(business);
          return;
        }
        for (const key in obj) {
          try {
            const value = obj[key];
            if (Array.isArray(value)) {
              this.searchNestedArrays(value, businesses, depth + 1);
            } else if (typeof value === "object" && value !== null) {
              this.searchObjectForBusinessData(value, businesses, depth + 1);
            }
          } catch (error) {
          }
        }
      }
      /**
       * Search React component state
       */
      searchReactState(reactInstance, businesses, depth = 0) {
        if (depth > 5 || !reactInstance) return;
        try {
          if (reactInstance.state) {
            this.searchObjectForBusinessData(reactInstance.state, businesses, depth + 1);
          }
          if (reactInstance.props) {
            this.searchObjectForBusinessData(reactInstance.props, businesses, depth + 1);
          }
          if (reactInstance.child) {
            this.searchReactState(reactInstance.child, businesses, depth + 1);
          }
          if (reactInstance.sibling) {
            this.searchReactState(reactInstance.sibling, businesses, depth + 1);
          }
        } catch (error) {
        }
      }
      /**
       * Check if object looks like business data
       */
      isBusinessLikeObject(obj) {
        if (!obj || typeof obj !== "object") return false;
        const hasName = obj.name || obj.title || obj.businessName;
        const hasLocation = obj.address || obj.location || obj.vicinity || obj.formatted_address;
        const hasBusinessInfo = obj.phone || obj.rating || obj.website || obj.place_id;
        return !!(hasName && (hasLocation || hasBusinessInfo));
      }
      /**
       * Convert object to standardized business data
       */
      convertToBusinessData(obj) {
        var _a;
        try {
          const business = {};
          business.name = obj.name || obj.title || obj.businessName || "";
          if (!business.name) return null;
          business.address = obj.address || obj.formatted_address || obj.vicinity || obj.location || "";
          business.phone = obj.phone || obj.formatted_phone_number || obj.international_phone_number || "";
          business.website = obj.website || obj.url || "";
          business.rating = obj.rating || obj.averageRating;
          business.category = obj.category || obj.type || obj.types && obj.types[0] || "";
          business.placeId = obj.place_id || obj.placeId || obj.id;
          if ((_a = obj.geometry) == null ? void 0 : _a.location) {
            business.coordinates = {
              lat: obj.geometry.location.lat,
              lng: obj.geometry.location.lng
            };
          } else if (obj.lat && obj.lng) {
            business.coordinates = {
              lat: obj.lat,
              lng: obj.lng
            };
          }
          return business;
        } catch (error) {
          this.log(`Error converting to business data: ${error.message}`);
          return null;
        }
      }
      /**
       * Remove duplicate businesses
       */
      deduplicateBusinesses(businesses) {
        const seen = /* @__PURE__ */ new Set();
        return businesses.filter((business) => {
          const key = `${business.name}-${business.address}`.toLowerCase();
          if (seen.has(key)) return false;
          seen.add(key);
          return true;
        });
      }
      /**
       * Clear stored cache
       */
      clearCache() {
        this.apiResponses = [];
        this.log("🗑️ Cache cleared");
        window.postMessage({
          type: "GMAPS_CACHE_CLEARED",
          timestamp: Date.now()
        }, "*");
      }
      log(message) {
        if (this.debug) {
          console.log(`[GMapsInterceptor] ${message}`);
        }
      }
    }
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        new GoogleMapsInterceptor();
      });
    } else {
      new GoogleMapsInterceptor();
    }
  })();
})();
