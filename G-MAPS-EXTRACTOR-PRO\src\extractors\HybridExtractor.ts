// Hybrid Extractor - Combines all extraction methods for maximum reliability
import { BusinessData, SearchParams, FilterOptions } from '../types';

interface ExtractionStrategy {
  name: string;
  method: () => Promise<BusinessData[]>;
  priority: number;
  timeout: number;
}

interface ExtractionResult {
  businesses: BusinessData[];
  strategy: string;
  confidence: number;
  extractedAt: Date;
  errors?: string[];
}

interface APIResponse {
  url: string;
  data: any;
  timestamp: number;
}

export class HybridExtractor {
  private strategies: ExtractionStrategy[];
  private maxRetries: number = 3;
  private retryDelay: number = 2000;
  private debug: boolean = true;

  constructor() {
    this.strategies = [
      {
        name: 'Network',
        method: this.extractFromNetwork.bind(this),
        priority: 1,
        timeout: 5000
      },
      {
        name: 'Internal',
        method: this.extractFromInternal.bind(this),
        priority: 2,
        timeout: 3000
      },
      {
        name: 'DOM',
        method: this.extractFromDOM.bind(this),
        priority: 3,
        timeout: 10000
      }
    ];
  }

  /**
   * Main extraction method using hybrid approach
   */
  async extractBusinessData(
    searchParams: SearchParams,
    filterOptions: FilterOptions
  ): Promise<BusinessData[]> {
    this.log('🚀 Starting Hybrid Extraction...');
    
    const results: ExtractionResult[] = [];
    const errors: string[] = [];

    // Execute all strategies in parallel for speed
    const strategyPromises = this.strategies.map(async (strategy) => {
      try {
        this.log(`📡 Executing ${strategy.name} strategy...`);
        
        const startTime = Date.now();
        const businesses = await this.executeWithTimeout(
          strategy.method(),
          strategy.timeout
        );
        
        const executionTime = Date.now() - startTime;
        
        if (businesses && businesses.length > 0) {
          const result: ExtractionResult = {
            businesses,
            strategy: strategy.name,
            confidence: this.calculateStrategyConfidence(strategy.name, businesses),
            extractedAt: new Date(),
          };
          
          this.log(`✅ ${strategy.name}: Found ${businesses.length} businesses in ${executionTime}ms`);
          return result;
        } else {
          this.log(`⚠️ ${strategy.name}: No businesses found`);
          return null;
        }
      } catch (error) {
        const errorMsg = `❌ ${strategy.name} failed: ${error.message}`;
        this.log(errorMsg);
        errors.push(errorMsg);
        return null;
      }
    });

    // Wait for all strategies to complete
    const strategyResults = await Promise.allSettled(strategyPromises);
    
    // Collect successful results
    strategyResults.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      }
    });

    if (results.length === 0) {
      throw new Error(`All extraction strategies failed: ${errors.join('; ')}`);
    }

    // Merge and enhance data from all successful strategies
    const mergedBusinesses = this.mergeResults(results);
    
    // Apply filters
    const filteredBusinesses = this.applyFilters(mergedBusinesses, filterOptions);
    
    // Limit results
    const limitedBusinesses = filteredBusinesses.slice(0, searchParams.maxResults || 20);

    this.log(`🎯 Final result: ${limitedBusinesses.length} businesses from ${results.length} strategies`);
    
    return limitedBusinesses;
  }

  /**
   * Extract from intercepted network requests
   */
  private async extractFromNetwork(): Promise<BusinessData[]> {
    // Access intercepted API responses from injected script
    const apiResponses = await this.getStoredAPIResponses();
    const businesses: BusinessData[] = [];

    for (const response of apiResponses) {
      try {
        const extractedBusinesses = this.processAPIResponse(response);
        businesses.push(...extractedBusinesses);
      } catch (error) {
        this.log(`Error processing API response: ${error.message}`);
      }
    }

    return this.deduplicateBusinesses(businesses);
  }

  /**
   * Extract from Google Maps internal state
   */
  private async extractFromInternal(): Promise<BusinessData[]> {
    return new Promise((resolve) => {
      // Request data from injected script
      const messageHandler = (event: MessageEvent) => {
        if (event.source !== window) return;
        
        if (event.data.type === 'GMAPS_INTERNAL_RESPONSE') {
          window.removeEventListener('message', messageHandler);
          resolve(event.data.businesses || []);
        }
      };

      window.addEventListener('message', messageHandler);

      // Request internal data extraction
      window.postMessage({
        type: 'GMAPS_INTERNAL_REQUEST'
      }, '*');

      // Timeout after 3 seconds
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        resolve([]);
      }, 3000);
    });
  }

  /**
   * Extract from DOM elements
   */
  private async extractFromDOM(): Promise<BusinessData[]> {
    const businesses: BusinessData[] = [];

    // Enhanced selectors for different Google Maps layouts
    const resultSelectors = [
      '[data-result-index]',
      '.Nv2PK',
      '.bfdHYd',
      '.lI9IFe',
      '.VkpGBb',
      '.THOPZb',
      '[role="article"]',
      '.section-result',
      'a[href*="/place/"]',
      '.hfpxzc',
      '.NrDZNb',
      '.qrShPb'
    ];

    let resultElements: NodeListOf<Element> | null = null;

    // Try each selector until we find results
    for (const selector of resultSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        this.log(`Found ${elements.length} elements with selector: ${selector}`);
        resultElements = elements;
        break;
      }
    }

    if (!resultElements || resultElements.length === 0) {
      // Try to extract from current business view
      const currentBusiness = this.extractCurrentBusinessView();
      if (currentBusiness) {
        businesses.push(currentBusiness);
      }
      return businesses;
    }

    // Extract from each result element
    for (let i = 0; i < resultElements.length; i++) {
      try {
        const element = resultElements[i] as HTMLElement;
        const business = this.extractBusinessFromElement(element);
        
        if (business) {
          businesses.push(business);
        }
      } catch (error) {
        this.log(`Error extracting from element ${i}: ${error.message}`);
      }
    }

    return businesses;
  }

  /**
   * Extract business data from a single DOM element
   */
  private extractBusinessFromElement(element: HTMLElement): BusinessData | null {
    try {
      const name = this.extractTextFromElement(element, [
        '.qBF1Pd',
        '.fontHeadlineSmall',
        '.fontHeadlineLarge',
        'h3',
        '.section-result-title',
        '[data-value*="directions"]',
        '.DUwDvf.lfPIob',
        '.hfpxzc',
        '.NrDZNb'
      ]);

      if (!name) return null;

      const address = this.extractTextFromElement(element, [
        '.W4Efsd:nth-child(2)',
        '.Io6YTe',
        '.rogA2c',
        '.fontBodyMedium',
        '.section-result-location'
      ]);

      const category = this.extractTextFromElement(element, [
        '.W4Efsd:first-child',
        '.DkEaL',
        '.fontBodySmall',
        '.section-result-details'
      ]);

      const rating = this.extractRatingFromElement(element);
      const phone = this.extractPhoneFromElement(element);
      const website = this.extractWebsiteFromElement(element);

      return {
        id: this.generateBusinessId(),
        name,
        address: address || '',
        category: category || '',
        rating,
        phone: phone || '',
        website: website || '',
        extractedAt: new Date(),
        source: 'dom_extraction',
        confidence: this.calculateBusinessConfidence({ name, address, category, rating, phone, website })
      };
    } catch (error) {
      this.log(`Error extracting business from element: ${error.message}`);
      return null;
    }
  }

  /**
   * Extract text from element using multiple selectors
   */
  private extractTextFromElement(element: Element, selectors: string[]): string {
    for (const selector of selectors) {
      const found = element.querySelector(selector);
      if (found?.textContent?.trim()) {
        const text = found.textContent.trim();
        // Filter out common non-business text
        if (this.isValidBusinessText(text)) {
          return text;
        }
      }
    }
    return '';
  }

  /**
   * Validate if text is likely to be business-related
   */
  private isValidBusinessText(text: string): boolean {
    const invalidTexts = [
      'Google Maps', 'Search', 'Directions', 'Route', 'Navigate',
      'Menu', 'Photos', 'Reviews', 'Hours', 'Call', 'Website'
    ];
    
    return text.length > 1 && 
           text.length < 200 && 
           !invalidTexts.some(invalid => text.includes(invalid));
  }

  /**
   * Extract rating from element
   */
  private extractRatingFromElement(element: HTMLElement): number | undefined {
    const ratingSelectors = [
      '.MW4etd',
      '.ceNzKf',
      '[aria-label*="star"]',
      '.section-result-rating'
    ];

    for (const selector of ratingSelectors) {
      const ratingElement = element.querySelector(selector);
      if (ratingElement?.textContent) {
        const ratingMatch = ratingElement.textContent.match(/(\d+\.?\d*)/);
        if (ratingMatch) {
          const rating = parseFloat(ratingMatch[1]);
          if (rating >= 0 && rating <= 5) {
            return rating;
          }
        }
      }
    }

    return undefined;
  }

  /**
   * Extract phone from element
   */
  private extractPhoneFromElement(element: HTMLElement): string {
    const phoneSelectors = [
      '[data-item-id="phone"]',
      '[aria-label*="phone"]',
      '.section-result-phone'
    ];

    for (const selector of phoneSelectors) {
      const phoneElement = element.querySelector(selector);
      if (phoneElement?.textContent?.trim()) {
        return phoneElement.textContent.trim();
      }
    }

    return '';
  }

  /**
   * Extract website from element
   */
  private extractWebsiteFromElement(element: HTMLElement): string {
    const websiteSelectors = [
      '[data-item-id="authority"]',
      '[aria-label*="website"]',
      'a[href^="http"]'
    ];

    for (const selector of websiteSelectors) {
      const websiteElement = element.querySelector(selector) as HTMLAnchorElement;
      if (websiteElement?.href) {
        return websiteElement.href;
      }
    }

    return '';
  }

  /**
   * Extract current business view (when viewing a single business)
   */
  private extractCurrentBusinessView(): BusinessData | null {
    try {
      const name = this.extractTextFromElement(document, [
        'h1[data-attrid="title"]',
        '.x3AX1-LfntMc-header-title-title',
        '.qrShPb',
        '.DUwDvf.lfPIob'
      ]);

      if (!name) return null;

      const address = this.extractTextFromElement(document, [
        '[data-item-id="address"]',
        '.Io6YTe',
        '.rogA2c'
      ]);

      const phone = this.extractTextFromElement(document, [
        '[data-item-id="phone"]',
        '[aria-label*="phone"]'
      ]);

      const website = document.querySelector('[data-item-id="authority"] a')?.getAttribute('href') || '';

      return {
        id: this.generateBusinessId(),
        name,
        address: address || '',
        category: '',
        phone: phone || '',
        website,
        extractedAt: new Date(),
        source: 'current_view',
        confidence: 0.9
      };
    } catch (error) {
      this.log(`Error extracting current business view: ${error.message}`);
      return null;
    }
  }

  /**
   * Get stored API responses from injected script
   */
  private async getStoredAPIResponses(): Promise<APIResponse[]> {
    return new Promise((resolve) => {
      const messageHandler = (event: MessageEvent) => {
        if (event.source !== window) return;

        if (event.data.type === 'GMAPS_API_RESPONSES') {
          window.removeEventListener('message', messageHandler);
          resolve(event.data.responses || []);
        }
      };

      window.addEventListener('message', messageHandler);

      // Request API responses
      window.postMessage({
        type: 'GMAPS_GET_API_RESPONSES'
      }, '*');

      // Timeout after 2 seconds
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        resolve([]);
      }, 2000);
    });
  }

  /**
   * Process API response to extract business data
   */
  private processAPIResponse(response: APIResponse): BusinessData[] {
    const businesses: BusinessData[] = [];

    try {
      // Handle different API response formats
      const data = response.data;

      // Google Places API format
      if (data.results && Array.isArray(data.results)) {
        data.results.forEach((place: any) => {
          const business = this.convertPlaceToBusinessData(place);
          if (business) businesses.push(business);
        });
      }

      // Search API format
      if (data.d && Array.isArray(data.d)) {
        data.d.forEach((item: any) => {
          if (item[14] && Array.isArray(item[14])) {
            item[14].forEach((place: any) => {
              const business = this.convertSearchResultToBusinessData(place);
              if (business) businesses.push(business);
            });
          }
        });
      }

      // Other potential formats
      if (data.features && Array.isArray(data.features)) {
        data.features.forEach((feature: any) => {
          const business = this.convertFeatureToBusinessData(feature);
          if (business) businesses.push(business);
        });
      }

    } catch (error) {
      this.log(`Error processing API response: ${error.message}`);
    }

    return businesses;
  }

  /**
   * Convert Google Places API result to BusinessData
   */
  private convertPlaceToBusinessData(place: any): BusinessData | null {
    try {
      if (!place.name) return null;

      return {
        id: this.generateBusinessId(),
        name: place.name,
        address: place.formatted_address || place.vicinity || '',
        category: place.types?.[0]?.replace(/_/g, ' ') || '',
        rating: place.rating,
        reviewCount: place.user_ratings_total,
        phone: place.formatted_phone_number || place.international_phone_number || '',
        website: place.website || '',
        placeId: place.place_id,
        coordinates: place.geometry?.location ? {
          lat: place.geometry.location.lat,
          lng: place.geometry.location.lng
        } : undefined,
        extractedAt: new Date(),
        source: 'places_api',
        confidence: 0.95
      };
    } catch (error) {
      this.log(`Error converting place data: ${error.message}`);
      return null;
    }
  }

  /**
   * Convert search result to BusinessData
   */
  private convertSearchResultToBusinessData(result: any): BusinessData | null {
    try {
      // Google Maps search results have complex nested structure
      const name = result[11] || result[0];
      if (!name) return null;

      return {
        id: this.generateBusinessId(),
        name,
        address: result[2] || '',
        category: result[13] || '',
        rating: result[4]?.[7],
        reviewCount: result[4]?.[8],
        phone: result[178]?.[0]?.[0] || '',
        website: result[7]?.[0] || '',
        placeId: result[78],
        extractedAt: new Date(),
        source: 'search_api',
        confidence: 0.9
      };
    } catch (error) {
      this.log(`Error converting search result: ${error.message}`);
      return null;
    }
  }

  /**
   * Convert feature to BusinessData
   */
  private convertFeatureToBusinessData(feature: any): BusinessData | null {
    try {
      const properties = feature.properties;
      if (!properties?.name) return null;

      return {
        id: this.generateBusinessId(),
        name: properties.name,
        address: properties.address || '',
        category: properties.category || '',
        rating: properties.rating,
        phone: properties.phone || '',
        website: properties.website || '',
        extractedAt: new Date(),
        source: 'feature_api',
        confidence: 0.85
      };
    } catch (error) {
      this.log(`Error converting feature data: ${error.message}`);
      return null;
    }
  }

  /**
   * Merge results from multiple strategies
   */
  private mergeResults(results: ExtractionResult[]): BusinessData[] {
    const businessMap = new Map<string, BusinessData>();

    // Sort results by confidence (highest first)
    const sortedResults = results.sort((a, b) => b.confidence - a.confidence);

    for (const result of sortedResults) {
      for (const business of result.businesses) {
        const key = this.generateBusinessKey(business);

        if (!businessMap.has(key)) {
          // New business
          businessMap.set(key, {
            ...business,
            sources: [result.strategy],
            confidence: business.confidence || result.confidence
          });
        } else {
          // Merge with existing business
          const existing = businessMap.get(key)!;
          const merged = this.mergeBusiness(existing, business, result.strategy);
          businessMap.set(key, merged);
        }
      }
    }

    return Array.from(businessMap.values());
  }

  /**
   * Generate unique key for business deduplication
   */
  private generateBusinessKey(business: BusinessData): string {
    const name = business.name.toLowerCase().trim();
    const address = business.address.toLowerCase().trim();

    // Use name + first part of address for matching
    const addressPart = address.split(',')[0] || address.substring(0, 50);

    return `${name}-${addressPart}`;
  }

  /**
   * Merge two business objects
   */
  private mergeBusiness(existing: BusinessData, newBusiness: BusinessData, source: string): BusinessData {
    const merged = { ...existing };

    // Add source
    if (!merged.sources) merged.sources = [];
    if (!merged.sources.includes(source)) {
      merged.sources.push(source);
    }

    // Merge fields (prefer non-empty values)
    if (!merged.address && newBusiness.address) merged.address = newBusiness.address;
    if (!merged.category && newBusiness.category) merged.category = newBusiness.category;
    if (!merged.phone && newBusiness.phone) merged.phone = newBusiness.phone;
    if (!merged.website && newBusiness.website) merged.website = newBusiness.website;
    if (!merged.rating && newBusiness.rating) merged.rating = newBusiness.rating;
    if (!merged.reviewCount && newBusiness.reviewCount) merged.reviewCount = newBusiness.reviewCount;
    if (!merged.placeId && newBusiness.placeId) merged.placeId = newBusiness.placeId;
    if (!merged.coordinates && newBusiness.coordinates) merged.coordinates = newBusiness.coordinates;

    // Update confidence based on number of sources
    merged.confidence = Math.min(
      (merged.confidence || 0.5) + (merged.sources.length * 0.1),
      1.0
    );

    return merged;
  }

  /**
   * Remove duplicate businesses
   */
  private deduplicateBusinesses(businesses: BusinessData[]): BusinessData[] {
    const seen = new Set<string>();
    return businesses.filter(business => {
      const key = this.generateBusinessKey(business);
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  /**
   * Apply filters to businesses
   */
  private applyFilters(businesses: BusinessData[], filters: FilterOptions): BusinessData[] {
    return businesses.filter(business => {
      // Rating filter
      if (business.rating) {
        const [minRating, maxRating] = filters.ratingRange;
        if (business.rating < minRating || business.rating > maxRating) {
          return false;
        }
      }

      // Phone filter
      if (filters.hasPhone && !business.phone) {
        return false;
      }

      // Website filter
      if (filters.hasWebsite && !business.website) {
        return false;
      }

      // Confidence filter (only high-confidence results)
      if (business.confidence && business.confidence < 0.3) {
        return false;
      }

      return true;
    });
  }

  /**
   * Calculate confidence for a strategy based on results
   */
  private calculateStrategyConfidence(strategyName: string, businesses: BusinessData[]): number {
    let baseConfidence = 0.5;

    switch (strategyName) {
      case 'Network':
        baseConfidence = 0.95; // Highest confidence - direct from API
        break;
      case 'Internal':
        baseConfidence = 0.85; // High confidence - internal state
        break;
      case 'DOM':
        baseConfidence = 0.7;  // Lower confidence - DOM parsing
        break;
    }

    // Adjust based on data quality
    const avgDataCompleteness = businesses.reduce((sum, business) => {
      let completeness = 0;
      if (business.name) completeness += 0.3;
      if (business.address) completeness += 0.2;
      if (business.phone) completeness += 0.2;
      if (business.website) completeness += 0.15;
      if (business.rating) completeness += 0.15;
      return sum + completeness;
    }, 0) / businesses.length;

    return Math.min(baseConfidence * avgDataCompleteness, 1.0);
  }

  /**
   * Calculate confidence for individual business
   */
  private calculateBusinessConfidence(business: Partial<BusinessData>): number {
    let confidence = 0.5;

    if (business.name) confidence += 0.3;
    if (business.address) confidence += 0.2;
    if (business.phone) confidence += 0.15;
    if (business.website) confidence += 0.15;
    if (business.rating) confidence += 0.1;
    if (business.category) confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(promise: Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<T>((_, reject) =>
        setTimeout(() => reject(new Error('Operation timed out')), timeout)
      )
    ]);
  }

  private log(message: string): void {
    if (this.debug) {
      console.log(`[HybridExtractor] ${message}`);
    }
  }

  private generateBusinessId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }
}
