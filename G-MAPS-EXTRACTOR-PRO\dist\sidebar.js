import { d as createSvgIcon, j as jsxRuntimeExports, R as ReactDOM, T as ThemeProvider, l as lightTheme, C as CssBaseline, B as Box, a as CircularProgress, c as Typography, A as <PERSON><PERSON>, r as reactExports, b as Container, P as Paper, u as IconButton, D as DownloadIcon, e as Card, f as CardContent, S as SettingsIcon } from './index.js';
import { A as AppProvider, u as useApp, C as CloseIcon, T as Tabs, a as Tab, B as BusinessIcon, R as ReviewIcon, b as BusinessTab, c as ReviewsTab, S as SettingsTab } from './SettingsTab.js';
import { C as ChromeTabs } from './dataProcessing.js';

const RefreshIcon = createSvgIcon(/*#__PURE__*/jsxRuntimeExports.jsx("path", {
  d: "M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"
}));

function TabPanel({ children, value, index, ...other }) {
  return /* @__PURE__ */ jsxRuntimeExports.jsx(
    "div",
    {
      role: "tabpanel",
      hidden: value !== index,
      id: `popup-tabpanel-${index}`,
      "aria-labelledby": `popup-tab-${index}`,
      ...other,
      children: value === index && /* @__PURE__ */ jsxRuntimeExports.jsx(Box, { sx: { p: 2 }, children })
    }
  );
}
const SidebarContent = () => {
  const { state, setCurrentTab, setError } = useApp();
  const { extractedData: businesses, isLoading, error } = state;
  const handleExport = () => {
    const dataToExport = businesses;
    const jsonData = JSON.stringify(dataToExport, null, 2);
    const blob = new Blob([jsonData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `google-maps-businesses-${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  const handleRefresh = () => {
    window.location.reload();
  };
  const handleClose = () => {
    const sidebar = document.getElementById("gmaps-extractor-sidebar");
    if (sidebar) {
      sidebar.style.display = "none";
    }
  };
  if (isLoading) {
    return /* @__PURE__ */ jsxRuntimeExports.jsxs(Box, { display: "flex", justifyContent: "center", alignItems: "center", minHeight: "200px", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx(CircularProgress, {}),
      /* @__PURE__ */ jsxRuntimeExports.jsx(Typography, { variant: "body2", sx: { ml: 2 }, children: "Loading businesses..." })
    ] });
  }
  if (error) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Alert, { severity: "error", sx: { m: 2 }, children: error });
  }
  const [isGoogleMaps, setIsGoogleMaps] = reactExports.useState(false);
  const [checkingTab, setCheckingTab] = reactExports.useState(true);
  reactExports.useEffect(() => {
    const checkCurrentTab = async () => {
      try {
        const isGMaps = await ChromeTabs.isGoogleMapsTab();
        setIsGoogleMaps(isGMaps);
      } catch (error2) {
        console.error("Failed to check current tab:", error2);
        setError("Failed to check current tab");
      } finally {
        setCheckingTab(false);
      }
    };
    checkCurrentTab();
  }, [setError]);
  const handleTabChange = (_event, newValue) => {
    const tabs = ["business", "reviews", "settings"];
    setCurrentTab(tabs[newValue]);
  };
  const getCurrentTabIndex = () => {
    switch (state.currentTab) {
      case "business":
        return 0;
      case "reviews":
        return 1;
      case "settings":
        return 2;
      default:
        return 0;
    }
  };
  if (checkingTab) {
    return /* @__PURE__ */ jsxRuntimeExports.jsx(Box, { display: "flex", justifyContent: "center", alignItems: "center", minHeight: "200px", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CircularProgress, {}) });
  }
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(Container, { maxWidth: "sm", sx: { py: 2, height: "100vh", overflow: "auto" }, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsxs(Paper, { elevation: 2, sx: { p: 2, mb: 2 }, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Box, { display: "flex", justifyContent: "space-between", alignItems: "center", children: [
        /* @__PURE__ */ jsxRuntimeExports.jsx(Typography, { variant: "h6", component: "h1", sx: { flexGrow: 1 }, children: "🗺️ G Maps Extractor Pro" }),
        /* @__PURE__ */ jsxRuntimeExports.jsxs(Box, { children: [
          /* @__PURE__ */ jsxRuntimeExports.jsx(IconButton, { onClick: handleRefresh, size: "small", title: "Refresh", children: /* @__PURE__ */ jsxRuntimeExports.jsx(RefreshIcon, {}) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(IconButton, { onClick: handleExport, size: "small", title: "Export Data", children: /* @__PURE__ */ jsxRuntimeExports.jsx(DownloadIcon, {}) }),
          /* @__PURE__ */ jsxRuntimeExports.jsx(IconButton, { onClick: handleClose, size: "small", title: "Close", children: /* @__PURE__ */ jsxRuntimeExports.jsx(CloseIcon, {}) })
        ] })
      ] }),
      /* @__PURE__ */ jsxRuntimeExports.jsxs(Typography, { variant: "body2", color: "text.secondary", children: [
        "Found ",
        businesses.length,
        " businesses"
      ] })
    ] }),
    state.error && /* @__PURE__ */ jsxRuntimeExports.jsx(Alert, { severity: "error", sx: { mb: 2 }, onClose: () => setError(void 0), children: state.error }),
    !isGoogleMaps && /* @__PURE__ */ jsxRuntimeExports.jsx(Alert, { severity: "warning", sx: { mb: 2 }, children: "Please navigate to Google Maps to use the extraction features." }),
    /* @__PURE__ */ jsxRuntimeExports.jsx(Card, { children: /* @__PURE__ */ jsxRuntimeExports.jsxs(CardContent, { sx: { p: 0 }, children: [
      /* @__PURE__ */ jsxRuntimeExports.jsxs(
        Tabs,
        {
          value: getCurrentTabIndex(),
          onChange: handleTabChange,
          "aria-label": "sidebar tabs",
          variant: "fullWidth",
          sx: {
            borderBottom: 1,
            borderColor: "divider",
            "& .MuiTab-root": {
              minHeight: 48,
              fontSize: "0.875rem"
            }
          },
          children: [
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tab,
              {
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(BusinessIcon, {}),
                label: "Business Leads",
                id: "sidebar-tab-0",
                "aria-controls": "sidebar-tabpanel-0"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tab,
              {
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(ReviewIcon, {}),
                label: "Reviews & Photos",
                id: "sidebar-tab-1",
                "aria-controls": "sidebar-tabpanel-1"
              }
            ),
            /* @__PURE__ */ jsxRuntimeExports.jsx(
              Tab,
              {
                icon: /* @__PURE__ */ jsxRuntimeExports.jsx(SettingsIcon, {}),
                label: "Settings",
                id: "sidebar-tab-2",
                "aria-controls": "sidebar-tabpanel-2"
              }
            )
          ]
        }
      ),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TabPanel, { value: getCurrentTabIndex(), index: 0, children: /* @__PURE__ */ jsxRuntimeExports.jsx(BusinessTab, { isGoogleMaps }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TabPanel, { value: getCurrentTabIndex(), index: 1, children: /* @__PURE__ */ jsxRuntimeExports.jsx(ReviewsTab, { isGoogleMaps }) }),
      /* @__PURE__ */ jsxRuntimeExports.jsx(TabPanel, { value: getCurrentTabIndex(), index: 2, children: /* @__PURE__ */ jsxRuntimeExports.jsx(SettingsTab, {}) })
    ] }) })
  ] });
};
const Sidebar = () => {
  return /* @__PURE__ */ jsxRuntimeExports.jsxs(ThemeProvider, { theme: lightTheme, children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx(CssBaseline, {}),
    /* @__PURE__ */ jsxRuntimeExports.jsx(AppProvider, { children: /* @__PURE__ */ jsxRuntimeExports.jsx(SidebarContent, {}) })
  ] });
};
const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(/* @__PURE__ */ jsxRuntimeExports.jsx(Sidebar, {}));
